# Design Document

## Overview

Usercom is a conversational form AI application built with Next.js 15, Supabase, and modern web technologies. The system transforms traditional static forms into engaging, AI-powered conversations that businesses can deploy across multiple channels. The architecture follows a modular, scalable design with strong security, GDPR compliance, and a Typeform-inspired user interface.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[Next.js 15 App Router]
        B[React Components]
        C[Zustand State Management]
        D[Better-Auth Client]
    end
    
    subgraph "API Layer"
        E[Next.js API Routes]
        F[Authentication Middleware]
        G[Rate Limiting]
        H[Input Validation]
    end
    
    subgraph "AI Services"
        I[Vercel AI SDK]
        J[Google Vertex AI]
        K[Sentiment Analysis]
        L[RAG System]
    end
    
    subgraph "Database Layer"
        M[Supabase PostgreSQL]
        N[Vector Storage]
        O[Row-Level Security]
        P[Real-time Subscriptions]
    end
    
    subgraph "External Services"
        Q[Polar.sh Billing]
        R[Firecrawl Web Scraping]
        S[Slack Integration]
        T[Review Platforms]
    end
    
    A --> E
    E --> I
    E --> M
    I --> J
    M --> N
    E --> Q
    E --> R
    E --> S
```

### Technology Stack

- **Frontend**: Next.js 15 with App Router, React 18, TypeScript
- **Styling**: Tailwind CSS with Typeform-inspired design system
- **State Management**: Zustand for client-side state
- **Authentication**: Better-Auth with Supabase adapter
- **Database**: Supabase (PostgreSQL) with Row-Level Security
- **Vector Database**: Supabase Vector (pgvector)
- **AI/ML**: Vercel AI SDK with Google Vertex AI (Gemini provider)
- **Generative UI**: Vercel AI SDK's Generative UI for dynamic component rendering
- **Input Validation**: Zod for schema validation and type safety
- **Payments**: Polar.sh integration
- **Web Scraping**: Firecrawl API
- **Deployment**: Vercel with edge functions

## Components and Interfaces

### Core Components

#### 1. Authentication System
```typescript
interface AuthUser {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
  subscription_tier: 'free' | 'pro' | 'business';
  created_at: string;
  updated_at: string;
}

interface AuthSession {
  user: AuthUser;
  access_token: string;
  refresh_token: string;
  expires_at: number;
}
```

#### 2. Form Builder Interface
```typescript
interface ConversationalForm {
  id: string;
  user_id: string;
  title: string;
  description?: string;
  questions: FormQuestion[];
  ai_config: AIConfiguration;
  branding: FormBranding;
  deployment_settings: DeploymentSettings;
  analytics_config: AnalyticsConfig;
  created_at: string;
  updated_at: string;
}

interface FormQuestion {
  id: string;
  type: 'text' | 'multiple_choice' | 'rating' | 'email' | 'phone' | 'date';
  text: string;
  required: boolean;
  validation_rules?: ValidationRule[];
  branching_logic?: BranchingRule[];
  ai_context?: string;
  order: number;
}

interface AIConfiguration {
  tone: 'professional' | 'friendly' | 'casual' | 'formal';
  personality: string;
  knowledge_base_ids: string[];
  use_rag: boolean;
  custom_instructions?: string;
}
```

#### 3. Split-Screen Form Builder
```typescript
interface FormBuilderState {
  form: ConversationalForm;
  selectedQuestion?: string;
  previewMode: 'edit' | 'test';
  leftPanelTab: 'questions' | 'settings' | 'ai' | 'flow';
  isDirty: boolean;
  testSession?: TestSession;
}

interface TestSession {
  id: string;
  responses: Record<string, any>;
  current_question: string;
  is_mock: true;
  created_at: string;
}
```

#### 4. Conversational Interface
```typescript
interface ConversationState {
  session_id: string;
  form_id: string;
  current_question: FormQuestion;
  responses: ConversationResponse[];
  sentiment_score?: number;
  is_complete: boolean;
  routing_decision?: 'positive' | 'negative' | 'neutral';
}

interface ConversationResponse {
  question_id: string;
  response_text: string;
  response_data?: any;
  timestamp: string;
  ai_analysis?: {
    sentiment: number;
    confidence: number;
    next_question_id?: string;
  };
}
```

#### 5. Generative UI Components
```typescript
// Dynamic component rendering system
interface GenerativeUIComponent {
  type: 'BotMessage' | 'EmailInput' | 'GenericInput' | 'NameInput' | 'ScaleInput' | 'WebsiteInput' | 'YesNo';
  props: Record<string, any>;
  render: () => React.ReactNode;
  capture: (input: any) => Promise<ConversationResponse>;
}

// Component definitions
interface BotMessageProps {
  message: string;
  isStreaming?: boolean;
  avatar?: string;
}

interface EmailInputProps {
  placeholder?: string;
  required?: boolean;
  onSubmit: (email: string) => void;
}

interface GenericInputProps {
  placeholder: string;
  type?: 'text' | 'textarea';
  maxLength?: number;
  onSubmit: (value: string) => void;
}

interface NameInputProps {
  placeholder?: string;
  splitFirstLast?: boolean;
  onSubmit: (name: string | {first: string, last: string}) => void;
}

interface ScaleInputProps {
  min: number;
  max: number;
  labels?: {min: string, max: string};
  onSubmit: (rating: number) => void;
}

interface WebsiteInputProps {
  placeholder?: string;
  validateUrl?: boolean;
  onSubmit: (url: string) => void;
}

interface YesNoProps {
  question: string;
  yesLabel?: string;
  noLabel?: string;
  onSubmit: (answer: boolean) => void;
}

// Dynamic component registry
const componentRegistry: Record<string, GenerativeUIComponent> = {
  BotMessage: {
    type: 'BotMessage',
    render: (props: BotMessageProps) => <BotMessage {...props} />,
    capture: async (input) => ({ /* AI message processing */ })
  },
  EmailInput: {
    type: 'EmailInput',
    render: (props: EmailInputProps) => <EmailInput {...props} />,
    capture: async (email: string) => ({
      question_id: 'current',
      response_text: email,
      response_data: { email, validated: isValidEmail(email) },
      timestamp: new Date().toISOString()
    })
  },
  // ... other components
};
```

### Generative UI Architecture

#### Dynamic Component Rendering System

The conversational form system uses Vercel AI SDK's Generative UI approach to dynamically render form components based on AI decisions and conversation flow.

```typescript
// AI-driven component selection
interface AIComponentDecision {
  component_type: 'BotMessage' | 'EmailInput' | 'GenericInput' | 'NameInput' | 'ScaleInput' | 'WebsiteInput' | 'YesNo';
  props: Record<string, any>;
  reasoning: string;
  next_expected_input: string;
}

// Streaming AI response with component generation
const generateConversationalUI = async (
  conversation: ConversationState,
  userInput: string
) => {
  const stream = await ai.generateUI({
    model: 'gemini-pro',
    messages: buildConversationHistory(conversation),
    tools: {
      renderBotMessage: tool({
        description: 'Display an AI message to the user',
        parameters: z.object({
          message: z.string(),
          isStreaming: z.boolean().optional()
        }),
        generate: async ({ message, isStreaming }) => (
          <BotMessage message={message} isStreaming={isStreaming} />
        )
      }),
      renderEmailInput: tool({
        description: 'Collect user email address',
        parameters: z.object({
          placeholder: z.string().optional(),
          required: z.boolean().optional()
        }),
        generate: async ({ placeholder, required }) => (
          <EmailInput 
            placeholder={placeholder} 
            required={required}
            onSubmit={handleEmailSubmit}
          />
        )
      }),
      renderScaleInput: tool({
        description: 'Collect rating or satisfaction score',
        parameters: z.object({
          min: z.number(),
          max: z.number(),
          labels: z.object({
            min: z.string(),
            max: z.string()
          }).optional()
        }),
        generate: async ({ min, max, labels }) => (
          <ScaleInput 
            min={min} 
            max={max} 
            labels={labels}
            onSubmit={handleScaleSubmit}
          />
        )
      }),
      // ... other component tools
    }
  });
  
  return stream;
};
```

#### Component Use Cases and Implementation

```typescript
// BotMessage.tsx - AI message display with streaming
interface BotMessageComponent {
  message: string;
  isStreaming: boolean;
  avatar?: string;
  timestamp: string;
}

// EmailInput.tsx - Email collection with validation
interface EmailInputComponent {
  placeholder: string;
  required: boolean;
  validation: {
    pattern: RegExp;
    message: string;
  };
  onSubmit: (email: string) => Promise<void>;
}

// GenericInput.tsx - Flexible text input
interface GenericInputComponent {
  placeholder: string;
  type: 'text' | 'textarea' | 'number';
  maxLength?: number;
  validation?: ValidationRule[];
  onSubmit: (value: string) => Promise<void>;
}

// NameInput.tsx - Name collection
interface NameInputComponent {
  placeholder: string;
  splitFirstLast: boolean;
  onSubmit: (name: string | NameParts) => Promise<void>;
}

// ScaleInput.tsx - Rating scale (0-10)
interface ScaleInputComponent {
  min: number;
  max: number;
  step: number;
  labels: {
    min: string;
    max: string;
  };
  showNumbers: boolean;
  onSubmit: (rating: number) => Promise<void>;
}

// WebsiteInput.tsx - URL collection
interface WebsiteInputComponent {
  placeholder: string;
  validateUrl: boolean;
  allowedDomains?: string[];
  onSubmit: (url: string) => Promise<void>;
}

// YesNo.tsx - Binary choice
interface YesNoComponent {
  question: string;
  yesLabel: string;
  noLabel: string;
  variant: 'buttons' | 'toggle' | 'radio';
  onSubmit: (answer: boolean) => Promise<void>;
}
```

### UI Component Architecture

#### Design System (Typeform-inspired)
```typescript
// Design tokens
const designTokens = {
  colors: {
    primary: '#6366f1',
    secondary: '#8b5cf6',
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    neutral: {
      50: '#f9fafb',
      100: '#f3f4f6',
      // ... more shades
    }
  },
  borderRadius: {
    sm: '0.375rem',
    md: '0.5rem',
    lg: '0.75rem',
    xl: '1rem',
    '2xl': '1.5rem',
  },
  typography: {
    fontFamily: 'Inter, system-ui, sans-serif',
    // ... font scales
  }
};

// Component interfaces
interface ButtonProps {
  variant: 'primary' | 'secondary' | 'outline' | 'ghost';
  size: 'sm' | 'md' | 'lg';
  rounded?: boolean;
  loading?: boolean;
  children: React.ReactNode;
}

interface FormFieldProps {
  label: string;
  type: 'text' | 'email' | 'select' | 'textarea';
  placeholder?: string;
  error?: string;
  required?: boolean;
  rounded?: boolean;
}
```

## Data Models

### Database Schema

#### Users and Authentication
```sql
-- Users table
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  name VARCHAR(255),
  avatar_url TEXT,
  subscription_tier VARCHAR(20) DEFAULT 'free',
  subscription_id VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User sessions
CREATE TABLE user_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  session_token VARCHAR(255) UNIQUE NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Forms and Questions
```sql
-- Forms table
CREATE TABLE forms (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  ai_config JSONB DEFAULT '{}',
  branding JSONB DEFAULT '{}',
  deployment_settings JSONB DEFAULT '{}',
  analytics_config JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Questions table
CREATE TABLE form_questions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  form_id UUID REFERENCES forms(id) ON DELETE CASCADE,
  type VARCHAR(50) NOT NULL,
  text TEXT NOT NULL,
  required BOOLEAN DEFAULT false,
  validation_rules JSONB DEFAULT '[]',
  branching_logic JSONB DEFAULT '[]',
  ai_context TEXT,
  order_index INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Conversations and Responses
```sql
-- Conversation sessions
CREATE TABLE conversation_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  form_id UUID REFERENCES forms(id) ON DELETE CASCADE,
  user_agent TEXT,
  ip_address INET,
  referrer TEXT,
  deployment_channel VARCHAR(50),
  is_complete BOOLEAN DEFAULT false,
  sentiment_score DECIMAL(3,2),
  routing_decision VARCHAR(20),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- Individual responses
CREATE TABLE conversation_responses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id UUID REFERENCES conversation_sessions(id) ON DELETE CASCADE,
  question_id UUID REFERENCES form_questions(id) ON DELETE CASCADE,
  response_text TEXT,
  response_data JSONB,
  ai_analysis JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Knowledge Base and Vector Storage
```sql
-- Knowledge base documents
CREATE TABLE knowledge_documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  content TEXT NOT NULL,
  document_type VARCHAR(50),
  source_url TEXT,
  file_path TEXT,
  embedding vector(1536), -- OpenAI embedding dimension
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create vector similarity search index
CREATE INDEX ON knowledge_documents USING ivfflat (embedding vector_cosine_ops);
```

### Row-Level Security (RLS) Policies

```sql
-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE forms ENABLE ROW LEVEL SECURITY;
ALTER TABLE form_questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversation_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE knowledge_documents ENABLE ROW LEVEL SECURITY;

-- Users can only access their own data
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth.uid() = id);

-- Forms access policies
CREATE POLICY "Users can manage own forms" ON forms
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own form questions" ON form_questions
  FOR ALL USING (
    auth.uid() = (SELECT user_id FROM forms WHERE forms.id = form_questions.form_id)
  );

-- Conversation sessions - users can view their form responses
CREATE POLICY "Users can view own form responses" ON conversation_sessions
  FOR SELECT USING (
    auth.uid() = (SELECT user_id FROM forms WHERE forms.id = conversation_sessions.form_id)
  );
```

## Error Handling

### Error Types and Responses
```typescript
interface APIError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

// Error codes
enum ErrorCodes {
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  NOT_FOUND = 'NOT_FOUND',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  RATE_LIMITED = 'RATE_LIMITED',
  AI_SERVICE_ERROR = 'AI_SERVICE_ERROR',
  SUBSCRIPTION_LIMIT = 'SUBSCRIPTION_LIMIT',
  GDPR_VIOLATION = 'GDPR_VIOLATION'
}

// Error handling middleware
const errorHandler = (error: Error, req: Request, res: Response) => {
  const apiError: APIError = {
    code: error.name || 'INTERNAL_ERROR',
    message: error.message,
    timestamp: new Date().toISOString()
  };
  
  // Log error without sensitive data
  logger.error('API Error', {
    code: apiError.code,
    message: apiError.message,
    path: req.path,
    method: req.method,
    user_id: req.user?.id
  });
  
  res.status(getStatusCode(error)).json(apiError);
};
```

### Custom Error Pages
- **404 Page**: Friendly not found page with navigation back to dashboard
- **500 Page**: Generic error page with support contact information
- **403 Page**: Subscription upgrade prompt for feature limitations

## Testing Strategy

### Unit Testing
- **Components**: React Testing Library for UI components
- **API Routes**: Jest for API endpoint testing
- **Utilities**: Jest for helper functions and utilities
- **Database**: Supabase local testing environment

### Integration Testing
- **Form Builder**: End-to-end form creation and editing
- **Conversation Flow**: Complete conversation simulation
- **Authentication**: Login/logout and session management
- **Billing**: Subscription upgrade/downgrade flows

### E2E Testing
- **Playwright**: Full user journey testing
- **Form Deployment**: QR code, embed, and link testing
- **Multi-device**: Responsive design validation
- **Performance**: Core Web Vitals monitoring

### Testing Environments
```typescript
// Test configuration
interface TestConfig {
  database_url: string;
  ai_mock_mode: boolean;
  billing_mock_mode: boolean;
  email_mock_mode: boolean;
}

// Mock AI responses for testing
const mockAIService = {
  generateResponse: (input: string) => Promise.resolve({
    text: "Mock AI response",
    sentiment: 0.8,
    confidence: 0.9
  }),
  
  analyzeSentiment: (text: string) => Promise.resolve({
    score: 0.7,
    label: 'positive'
  })
};
```

## Security Implementation

### Authentication & Authorization
```typescript
// Better-Auth configuration
const authConfig = {
  database: supabaseAdapter,
  providers: {
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    },
    email: {
      enabled: true,
      passwordValidation: {
        minLength: 8,
        requireUppercase: true,
        requireNumbers: true,
        requireSpecialChars: true
      }
    }
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
  },
  security: {
    rateLimit: {
      window: 15 * 60 * 1000, // 15 minutes
      max: 5 // 5 attempts per window
    }
  }
};
```

### Input Validation & Sanitization
```typescript
import { z } from 'zod';

// Form validation schemas
const createFormSchema = z.object({
  title: z.string().min(1).max(255),
  description: z.string().max(1000).optional(),
  questions: z.array(z.object({
    type: z.enum(['text', 'multiple_choice', 'rating', 'email', 'phone', 'date']),
    text: z.string().min(1).max(500),
    required: z.boolean().default(false)
  })).min(1).max(50)
});

// API route with validation
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const validatedData = createFormSchema.parse(body);
    
    // Process validated data
    return NextResponse.json({ success: true });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }
    throw error;
  }
}
```

### GDPR Compliance Implementation
```typescript
// GDPR service
class GDPRService {
  async exportUserData(userId: string): Promise<UserDataExport> {
    const userData = await this.collectAllUserData(userId);
    return {
      user_profile: userData.profile,
      forms: userData.forms,
      responses: userData.responses,
      knowledge_documents: userData.documents,
      export_date: new Date().toISOString()
    };
  }
  
  async deleteUserData(userId: string): Promise<void> {
    // Cascade delete with audit trail
    await this.auditLog('data_deletion_requested', { userId });
    
    // Delete in correct order to maintain referential integrity
    await supabase.from('conversation_responses').delete().eq('user_id', userId);
    await supabase.from('conversation_sessions').delete().eq('user_id', userId);
    await supabase.from('form_questions').delete().eq('user_id', userId);
    await supabase.from('forms').delete().eq('user_id', userId);
    await supabase.from('knowledge_documents').delete().eq('user_id', userId);
    await supabase.from('users').delete().eq('id', userId);
    
    await this.auditLog('data_deletion_completed', { userId });
  }
  
  async handleDataBreach(incident: BreachIncident): Promise<void> {
    // Notify authorities within 72 hours
    await this.notifyAuthorities(incident);
    
    // Notify affected users
    const affectedUsers = await this.getAffectedUsers(incident);
    await this.notifyUsers(affectedUsers, incident);
    
    // Log incident
    await this.auditLog('data_breach_handled', incident);
  }
}
```

## Performance Optimization

### Caching Strategy
```typescript
// Redis caching for frequently accessed data
const cacheConfig = {
  form_definitions: { ttl: 3600 }, // 1 hour
  user_sessions: { ttl: 1800 }, // 30 minutes
  ai_responses: { ttl: 300 }, // 5 minutes
  analytics_data: { ttl: 900 } // 15 minutes
};

// Next.js caching
export const revalidate = 3600; // Revalidate every hour
export const dynamic = 'force-dynamic'; // For user-specific content
```

### Database Optimization
```sql
-- Indexes for performance
CREATE INDEX idx_forms_user_id ON forms(user_id);
CREATE INDEX idx_form_questions_form_id ON form_questions(form_id);
CREATE INDEX idx_conversation_sessions_form_id ON conversation_sessions(form_id);
CREATE INDEX idx_conversation_responses_session_id ON conversation_responses(session_id);
CREATE INDEX idx_knowledge_documents_user_id ON knowledge_documents(user_id);

-- Partial indexes for active forms
CREATE INDEX idx_active_forms ON forms(user_id) WHERE is_active = true;
```

### Code Splitting & Lazy Loading
```typescript
// Lazy load heavy components
const FormBuilder = lazy(() => import('@/components/FormBuilder'));
const Analytics = lazy(() => import('@/components/Analytics'));
const AIConfiguration = lazy(() => import('@/components/AIConfiguration'));

// Route-based code splitting with Next.js App Router
// Automatic with the app directory structure
```

## Complete User Flows

### 1. User Registration & Authentication Flow

**Flow**: Landing Page → Sign Up/Sign In (Better Auth Google with Supabase adapter) → Dashboard

**Implementation Details**:
- Better Auth handles all authentication with Google OAuth and email/password
- Users stored in Supabase with token identifier mapping
- Protected routes require authentication middleware
- Automatic redirect to dashboard after successful authentication

```typescript
// Authentication flow implementation
const authFlow = {
  landingPage: '/',
  signIn: '/auth/signin',
  signUp: '/auth/signup',
  dashboard: '/dashboard',
  protectedRoutes: ['/dashboard', '/forms', '/analytics', '/settings']
};

// Better Auth configuration with Supabase
const authConfig = {
  database: supabaseAdapter({
    url: process.env.SUPABASE_URL,
    key: process.env.SUPABASE_ANON_KEY
  }),
  providers: {
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      redirectURI: `${process.env.NEXT_PUBLIC_APP_URL}/auth/callback/google`
    }
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
  },
  security: {
    rateLimit: {
      window: 15 * 60 * 1000, // 15 minutes
      max: 5 // 5 attempts per window
    }
  }
};
```

### 2. Form Creation Flow (AI-Powered)

**Flow**: Dashboard → "Generate Form" Button → Prompt Modal → AI Generation → Form Editor

**Detailed Steps**:

1. **Prompt Input**: User writes natural language description of their form, problem they're facing, and what they need to collect
2. **AI Processing**:
   - Prompt sent to `/api/forms/generate`
   - Google Gemini 2.0 Flash processes using structured generation
   - AI returns JSON with title, description, and questions
   - Conversational two-way capability: users can ask questions and AI form will answer as well as ask questions
   - Additional information captured beyond set questions
3. **Form Saving**: Generated form saved to Supabase database
4. **Redirect**: User taken to form editor page

```typescript
// AI Form Generation API with latest Vercel AI SDK patterns
export async function POST(request: Request) {
  const { prompt, userId } = await request.json();
  
  const result = await generateObject({
    model: google('gemini-2.0-flash-exp'),
    schema: z.object({
      title: z.string(),
      description: z.string(),
      questions: z.array(z.object({
        id: z.string(),
        type: z.enum(['text', 'email', 'rating', 'multiple_choice', 'yes_no']),
        text: z.string(),
        required: z.boolean(),
        ai_context: z.string().optional()
      })),
      ai_config: z.object({
        tone: z.enum(['professional', 'friendly', 'casual', 'formal']),
        personality: z.string(),
        conversational_mode: z.boolean().default(true)
      })
    }),
    prompt: `Generate a conversational form based on this description: ${prompt}. 
             Make it engaging and natural, with follow-up capabilities.`
  });
  
  // Save to Supabase and return form ID
  const form = await supabase.from('forms').insert({
    user_id: userId,
    ...result.object
  }).select().single();
  
  return NextResponse.json({ formId: form.id });
}
```

### 3. Form Management Flow

**Flow**: Dashboard → Forms List → Select Form → Form Editor (3 tabs)

**Form Editor Tabs**:

1. **Form Fields Tab**:
   - Left panel: Edit questions, add/remove fields, reorder
   - Right panel: Live preview of form interface
2. **Form Settings Tab**:
   - Configure form settings (anonymous responses, email collection, etc.)
   - Same live preview
3. **Responses Tab**:
   - View all submissions
   - Analytics dashboard
   - Export capabilities

```typescript
// Form Builder State Management with Zustand
interface FormBuilderState {
  activeTab: 'fields' | 'settings' | 'responses';
  form: ConversationalForm;
  previewMode: 'edit' | 'test';
  leftPanel: {
    selectedQuestion?: string;
    draggedQuestion?: string;
    isReordering: boolean;
  };
  rightPanel: {
    conversationState: ConversationState;
    testSession?: TestSession;
  };
}

// Split-screen layout component
const FormBuilder = () => {
  const [state, setState] = useFormBuilderState();
  
  return (
    <div className="flex h-screen">
      {/* Left Panel - Editing Controls */}
      <div className="w-1/2 border-r bg-white">
        <FormEditingPanel 
          form={state.form}
          activeTab={state.activeTab}
          onQuestionSelect={handleQuestionSelect}
          onQuestionReorder={handleQuestionReorder}
        />
      </div>
      
      {/* Right Panel - Live Preview */}
      <div className="w-1/2 bg-gray-50">
        <FormPreviewPanel 
          form={state.form}
          mode={state.previewMode}
          testSession={state.rightPanel.testSession}
        />
      </div>
    </div>
  );
};
```

### 4. Form Publishing Flow

**Flow**: Form Editor → Publish Button → Status Change → Share Link Generation

**Publishing States**:
- **Draft**: Only creator can view/edit
- **Published**: Public access via share link
- **Archived**: Hidden from main dashboard

```typescript
// Publishing system with Polar.sh integration
const publishForm = async (formId: string, status: 'draft' | 'published' | 'archived') => {
  const { data } = await supabase
    .from('forms')
    .update({ 
      status,
      published_at: status === 'published' ? new Date().toISOString() : null,
      share_url: status === 'published' ? generateShareUrl(formId) : null
    })
    .eq('id', formId)
    .select()
    .single();
    
  return data;
};
```

### 5. Form Filling Flow (The Core Innovation)

**Flow**: Share Link → Welcome Screen → One question at a time → Completion Screen

**Detailed Chat Experience**:

- **Welcome Screen**: Shows form title, description, question count
- **One question streaming at a time Interface**:
  - Questions presented one at a time
  - Natural conversation flow
  - "Thinking" animations between questions
  - Progress bar at top
  - Adaptive input types based on field type

**Smart Input Handling Components**:
- **BotMessage**: AI message display with streaming support
- **EmailInput**: Email validation and collection
- **GenericInput**: Flexible text input for various data types
- **NameInput**: Name collection with first/last name splitting
- **ScaleInput**: Interactive 0-10 rating scale with clickable buttons
- **WebsiteInput**: Website URL input with validation
- **YesNo**: Binary choice component with multiple variants

**Completion**: Thank you message → Summary screen

```typescript
// Conversational Form Interface using latest useChat patterns
const ConversationalForm = ({ formId }: { formId: string }) => {
  const { messages, sendMessage, status } = useChat({
    transport: new DefaultChatTransport({
      api: `/api/forms/${formId}/chat`,
    }),
    initialMessages: [
      {
        id: 'welcome',
        role: 'assistant',
        content: 'Welcome! I have a few questions for you. Let\'s get started!'
      }
    ]
  });

  return (
    <div className="max-w-2xl mx-auto p-6">
      {/* Progress Bar */}
      <ProgressBar current={messages.length} total={totalQuestions} />
      
      {/* Conversation Messages */}
      <div className="space-y-4 mb-6">
        {messages.map((message) => (
          <ConversationMessage 
            key={message.id} 
            message={message}
            isStreaming={status === 'streaming' && message.id === messages[messages.length - 1]?.id}
          />
        ))}
      </div>
      
      {/* Input Area */}
      <ConversationInput 
        onSubmit={sendMessage}
        disabled={status !== 'ready'}
        currentQuestion={getCurrentQuestion()}
      />
    </div>
  );
};

// Dynamic input component rendering with Generative UI
const ConversationInput = ({ currentQuestion, onSubmit }) => {
  const renderInputComponent = () => {
    switch (currentQuestion.type) {
      case 'email':
        return <EmailInput onSubmit={onSubmit} />;
      case 'rating':
        return <ScaleInput min={0} max={10} onSubmit={onSubmit} />;
      case 'yes_no':
        return <YesNo onSubmit={onSubmit} />;
      default:
        return <GenericInput onSubmit={onSubmit} />;
    }
  };
  
  return (
    <div className="bg-white rounded-2xl border p-4 shadow-sm">
      {renderInputComponent()}
    </div>
  );
};
```

### 6. Response Analytics Flow

**Flow**: Dashboard → Form → Responses Tab → Individual Response Details

**Features**:
- Transcription of the conversation with questions and answers
- Table generated by extracting key information for each question
- Additional information gathered shown in 'Additional Information' row
- Response count and trends
- Completion rates
- Average response time
- Individual response viewing
- Export functionality

```typescript
// Analytics Dashboard with message metadata
const ResponseAnalytics = ({ formId }: { formId: string }) => {
  const { data: analytics } = useQuery({
    queryKey: ['analytics', formId],
    queryFn: () => fetchFormAnalytics(formId)
  });

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-4 gap-4">
        <MetricCard title="Total Responses" value={analytics.totalResponses} />
        <MetricCard title="Completion Rate" value={`${analytics.completionRate}%`} />
        <MetricCard title="Avg. Response Time" value={analytics.avgResponseTime} />
        <MetricCard title="Satisfaction Score" value={analytics.avgSatisfaction} />
      </div>
      
      {/* Response List */}
      <ResponseTable 
        responses={analytics.responses}
        onViewDetails={handleViewDetails}
        onExport={handleExport}
      />
    </div>
  );
};

// Individual Response Details with conversation transcript
const ResponseDetails = ({ responseId }: { responseId: string }) => {
  const { data: response } = useQuery({
    queryKey: ['response', responseId],
    queryFn: () => fetchResponseDetails(responseId)
  });

  return (
    <div className="space-y-6">
      {/* Conversation Transcript */}
      <div className="bg-white rounded-lg border p-6">
        <h3 className="text-lg font-semibold mb-4">Conversation Transcript</h3>
        <ConversationTranscript messages={response.conversation} />
      </div>
      
      {/* Extracted Data Table */}
      <div className="bg-white rounded-lg border p-6">
        <h3 className="text-lg font-semibold mb-4">Extracted Information</h3>
        <DataExtractionTable 
          questions={response.questions}
          answers={response.answers}
          additionalInfo={response.additionalInfo}
        />
      </div>
    </div>
  );
};
```

## Key Technical Implementations

### AI Form Generation with Latest Vercel AI SDK

```typescript
// Structured generation with Zod schemas and error handling
const formGenerationSchema = z.object({
  title: z.string().min(1).max(255),
  description: z.string().max(1000),
  questions: z.array(z.object({
    id: z.string(),
    type: z.enum(['text', 'email', 'rating', 'multiple_choice', 'yes_no', 'phone', 'date']),
    text: z.string(),
    required: z.boolean(),
    validation_rules: z.array(z.any()).optional(),
    ai_context: z.string().optional()
  })).min(1).max(50),
  ai_config: z.object({
    tone: z.enum(['professional', 'friendly', 'casual', 'formal']),
    personality: z.string(),
    conversational_mode: z.boolean(),
    follow_up_questions: z.boolean()
  })
});

// Token usage tracking with message metadata
const trackTokenUsage = async (userId: string, tokens: number, operation: string) => {
  await supabase.from('token_usage').insert({
    user_id: userId,
    tokens_used: tokens,
    operation,
    timestamp: new Date().toISOString()
  });
};

// Enhanced error handling
export async function POST(request: Request) {
  try {
    const { prompt, userId } = await request.json();
    
    const result = await generateObject({
      model: google('gemini-2.0-flash-exp'),
      schema: formGenerationSchema,
      prompt: `Generate a conversational form: ${prompt}`,
      onFinish: ({ usage }) => {
        trackTokenUsage(userId, usage.totalTokens, 'form_generation');
      }
    });
    
    return NextResponse.json({ success: true, form: result.object });
  } catch (error) {
    console.error('Form generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate form' },
      { status: 500 }
    );
  }
}
```

### Real-time Conversational Interface

```typescript
// Enhanced conversation flow with Framer Motion animations
const useConversationFlow = (formId: string) => {
  const [state, setState] = useState<ConversationState>({
    currentQuestionIndex: 0,
    responses: [],
    isComplete: false,
    progress: 0
  });

  const nextQuestion = useCallback(() => {
    setState(prev => ({
      ...prev,
      currentQuestionIndex: prev.currentQuestionIndex + 1,
      progress: ((prev.currentQuestionIndex + 1) / totalQuestions) * 100
    }));
  }, [totalQuestions]);

  return { state, nextQuestion };
};

// Smooth animations with Framer Motion
const QuestionTransition = ({ children, questionIndex }) => {
  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={questionIndex}
        initial={{ opacity: 0, x: 50 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: -50 }}
        transition={{ duration: 0.3, ease: "easeInOut" }}
      >
        {children}
      </motion.div>
    </AnimatePresence>
  );
};

// Auto navigation with Enter key support
const handleKeyPress = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault();
    submitResponse();
  }
};
```

### Preview System with Mock AI Responses

```typescript
// Enhanced preview system with transient data parts
const FormPreview = ({ form, isTestMode }: { form: ConversationalForm, isTestMode: boolean }) => {
  const [testSession, setTestSession] = useState<TestSession | null>(null);

  useEffect(() => {
    if (isTestMode) {
      // Create mock test session that doesn't consume credits or store data
      setTestSession({
        id: `test-${Date.now()}`,
        responses: {},
        current_question: form.questions[0]?.id,
        is_mock: true,
        created_at: new Date().toISOString()
      });
    }
  }, [isTestMode, form]);

  return (
    <div className="h-full bg-gray-50 p-4">
      {isTestMode && (
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-3 py-2 rounded mb-4">
          Preview Mode - No data will be saved or credits consumed
        </div>
      )}
      
      <ConversationalFormInterface 
        form={form}
        testSession={testSession}
        onResponse={isTestMode ? handleTestResponse : handleRealResponse}
      />
    </div>
  );
};

// Mock AI responses for testing with sentiment simulation
const mockAIResponse = (input: string, question: FormQuestion) => {
  return {
    text: `Thank you for your response: "${input}". Let me ask you the next question.`,
    sentiment: Math.random() * 2 - 1, // Random sentiment between -1 and 1
    confidence: 0.8,
    next_question_id: getNextQuestionId(question.id)
  };
};
```

### Polar.sh Billing Integration

```typescript
// Polar.sh subscription management
interface PolarSubscription {
  id: string;
  status: 'active' | 'canceled' | 'past_due';
  current_period_end: string;
  plan: {
    id: string;
    name: string;
    price: number;
  };
}

const managePolarSubscription = async (userId: string, planId: string) => {
  try {
    const response = await fetch('https://api.polar.sh/v1/subscriptions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.POLAR_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        user_id: userId,
        plan_id: planId
      })
    });
    
    const subscription = await response.json();
    
    // Update user subscription in Supabase
    await supabase
      .from('users')
      .update({
        subscription_tier: subscription.plan.name.toLowerCase(),
        subscription_id: subscription.id
      })
      .eq('id', userId);
      
    return subscription;
  } catch (error) {
    console.error('Polar subscription error:', error);
    throw error;
  }
};
```

### Firecrawl Web Scraping Integration

```typescript
// Firecrawl integration for AI knowledge training
import FirecrawlApp from '@mendable/firecrawl-js';

const firecrawl = new FirecrawlApp({
  apiKey: process.env.FIRECRAWL_API_KEY
});

const scrapeWebsiteForKnowledge = async (url: string, userId: string) => {
  try {
    // Scrape the website with advanced options
    const scrapeResult = await firecrawl.scrapeUrl(url, {
      formats: ['markdown', 'html'],
      includeTags: ['main', 'article', 'section'],
      excludeTags: ['nav', 'footer', 'aside'],
      onlyMainContent: true,
      waitFor: 1000
    });
    
    if (scrapeResult.success) {
      // Generate embeddings for the scraped content
      const embedding = await generateEmbedding(scrapeResult.data.markdown);
      
      // Store in Supabase vector database
      await supabase.from('knowledge_documents').insert({
        user_id: userId,
        title: scrapeResult.data.metadata.title,
        content: scrapeResult.data.markdown,
        source_url: url,
        document_type: 'webpage',
        embedding: embedding
      });
      
      return { success: true, title: scrapeResult.data.metadata.title };
    }
    
    throw new Error('Scraping failed');
  } catch (error) {
    console.error('Firecrawl error:', error);
    throw error;
  }
};

// Batch crawling for comprehensive knowledge base
const crawlWebsiteForKnowledge = async (url: string, userId: string) => {
  try {
    const crawlResult = await firecrawl.crawlUrl(url, {
      limit: 100,
      scrapeOptions: {
        formats: ['markdown'],
        onlyMainContent: true
      }
    });
    
    if (crawlResult.success) {
      // Process each crawled page
      for (const page of crawlResult.data) {
        const embedding = await generateEmbedding(page.markdown);
        
        await supabase.from('knowledge_documents').insert({
          user_id: userId,
          title: page.metadata.title,
          content: page.markdown,
          source_url: page.metadata.sourceURL,
          document_type: 'webpage',
          embedding: embedding
        });
      }
      
      return { success: true, pages: crawlResult.data.length };
    }
    
    throw new Error('Crawling failed');
  } catch (error) {
    console.error('Firecrawl crawl error:', error);
    throw error;
  }
};
```

### Message Persistence and Chat History

```typescript
// Enhanced message persistence with validation
export async function POST(request: Request) {
  const { message, chatId } = await request.json();
  
  // Load previous messages from database
  const previousMessages = await loadChat(chatId);
  
  // Append new message to previous messages
  const messages = [...previousMessages, message];
  
  // Validate messages with tools and schemas
  const validatedMessages = await validateUIMessages({
    messages,
    tools: conversationalTools,
    dataPartsSchema: z.object({
      weather: z.object({
        city: z.string(),
        temperature: z.number().optional(),
        status: z.enum(['loading', 'success', 'error'])
      })
    })
  });
  
  const result = streamText({
    model: google('gemini-2.0-flash-exp'),
    messages: convertToModelMessages(validatedMessages),
    tools: conversationalTools,
    onFinish: ({ usage }) => {
      // Save chat with usage metadata
      saveChat({ 
        chatId, 
        messages: validatedMessages,
        metadata: { usage }
      });
    }
  });
  
  return result.toUIMessageStreamResponse({
    messageMetadata: ({ part }) => {
      if (part.type === 'finish') {
        return {
          totalTokens: part.totalUsage.totalTokens,
          model: part.response.modelId,
          finishReason: part.finishReason
        };
      }
    }
  });
}
```

This comprehensive design document now includes all the detailed user flows and technical implementations with the latest patterns from Vercel AI SDK 5.0, Polar.sh billing integration, and Firecrawl web scraping capabilities, providing a complete blueprint for building the Usercom conversational form application.