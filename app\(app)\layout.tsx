import { EnvVarWarning } from "@/components/env-var-warning";
import { AuthButton } from "@/components/auth-button";
import { ThemeSwitcher } from "@/components/theme-switcher";
import { hasEnvVars } from "@/lib/utils";
import Link from "next/link";

export default function AppLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Top Navigation */}
      <nav className="w-full border-b border-b-foreground/10 h-16 flex items-center px-6">
        <div className="w-full flex justify-between items-center">
          <div className="flex gap-6 items-center">
            <Link href="/dashboard" className="font-semibold text-lg">
              UserCom
            </Link>
            <div className="hidden md:flex gap-4 text-sm">
              <Link href="/dashboard" className="hover:text-foreground/80">
                Dashboard
              </Link>
              <Link href="/conversations" className="hover:text-foreground/80">
                Conversations
              </Link>
              <Link href="/customers" className="hover:text-foreground/80">
                Customers
              </Link>
              <Link href="/settings" className="hover:text-foreground/80">
                Settings
              </Link>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <ThemeSwitcher />
            {!hasEnvVars ? <EnvVarWarning /> : <AuthButton />}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="flex-1 flex">
        <div className="flex-1 p-6">
          {children}
        </div>
      </main>
    </div>
  );
}
