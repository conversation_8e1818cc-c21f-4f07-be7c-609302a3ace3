
# `experimental_useObject()`

<Note>
  `useObject` is an experimental feature and only available in React and Svelte.
</Note>

Allows you to consume text streams that represent a JSON object and parse them into a complete object based on a schema.
You can use it together with [`streamObject`](/docs/reference/ai-sdk-core/stream-object) in the backend.

```tsx
'use client';

import { experimental_useObject as useObject } from '@ai-sdk/react';

export default function Page() {
  const { object, submit } = useObject({
    api: '/api/use-object',
    schema: z.object({ content: z.string() }),
  });

  return (
    <div>
      <button onClick={() => submit('example input')}>Generate</button>
      {object?.content && <p>{object.content}</p>}
    </div>
  );
}
```

## Import

<Snippet
  text="import { experimental_useObject as useObject } from '@ai-sdk/react'"
  dark
  prompt={false}
/>

## API Signature

### Parameters

<PropertiesTable
  content={[
    {
      name: 'api',
      type: 'string',
      description:
        'The API endpoint that is called to generate objects. It should stream JSON that matches the schema as chunked text. It can be a relative path (starting with `/`) or an absolute URL.',
    },
    {
      name: 'schema',
      type: 'Zod Schema | JSON Schema',
      description:
        'A schema that defines the shape of the complete object. You can either pass in a Zod schema or a JSON schema (using the `jsonSchema` function).',
    },
    {
      name: 'id?',
      type: 'string',
      description:
        'A unique identifier. If not provided, a random one will be generated. When provided, the `useObject` hook with the same `id` will have shared states across components.',
    },
    {
      name: 'initialValue',
      type: 'DeepPartial<RESULT> | undefined',
      isOptional: true,
      description: 'An value for the initial object. Optional.',
    },
    {
      name: 'fetch',
      type: 'FetchFunction',
      isOptional: true,
      description:
        'A custom fetch function to be used for the API call. Defaults to the global fetch function. Optional.',
    },
    {
      name: 'headers',
      type: 'Record<string, string> | Headers',
      isOptional: true,
      description:
        'A headers object to be passed to the API endpoint. Optional.',
    },
    {
      name: 'credentials',
      type: 'RequestCredentials',
      isOptional: true,
      description:
        'The credentials mode to be used for the fetch request. Possible values are: "omit", "same-origin", "include". Optional.',
    },
    {
      name: 'onError',
      type: '(error: Error) => void',
      isOptional: true,
      description:
        'Callback function to be called when an error is encountered. Optional.',
    },
    {
      name: 'onFinish',
      type: '(result: OnFinishResult) => void',
      isOptional: true,
      description: 'Called when the streaming response has finished.',
      properties: [
        {
          type: 'OnFinishResult',
          parameters: [
            {
              name: 'object',
              type: 'T | undefined',
              description:
                'The generated object (typed according to the schema). Can be undefined if the final object does not match the schema.',
            },
            {
              name: 'error',
              type: 'unknown | undefined',
              description:
                'Optional error object. This is e.g. a TypeValidationError when the final object does not match the schema.',
            },
          ],
        },
      ],
    },
  ]}
/>

### Returns

<PropertiesTable
  content={[
    {
      name: 'submit',
      type: '(input: INPUT) => void',
      description: 'Calls the API with the provided input as JSON body.',
    },
    {
      name: 'object',
      type: 'DeepPartial<RESULT> | undefined',
      description:
        'The current value for the generated object. Updated as the API streams JSON chunks.',
    },
    {
      name: 'error',
      type: 'Error | unknown',
      description: 'The error object if the API call fails.',
    },
    {
      name: 'isLoading',
      type: 'boolean',
      description:
        'Boolean flag indicating whether a request is currently in progress.',
    },
    {
      name: 'stop',
      type: '() => void',
      description: 'Function to abort the current API request.',
    },
    {
      name: 'clear',
      type: '() => void',
      description: 'Function to clear the object state.',
    },
  ]}
/>

## Examples

<ExampleLinks
  examples={[
    {
      title: 'Streaming Object Generation with useObject',
      link: '/examples/next-pages/basics/streaming-object-generation',
    },
  ]}
/>
