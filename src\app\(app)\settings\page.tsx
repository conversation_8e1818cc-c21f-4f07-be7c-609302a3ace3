import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import { User, Bell, Shield, CreditCard } from "lucide-react";

export default async function SettingsPage() {
  const supabase = await createClient();

  const { data, error } = await supabase.auth.getClaims();
  if (error || !data?.claims) {
    redirect("/auth/login");
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold">Settings</h1>
        <p className="text-muted-foreground">
          Manage your account settings and preferences.
        </p>
      </div>

      {/* Settings Sections */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Profile Settings */}
        <div className="bg-card p-6 rounded-lg border">
          <div className="flex items-center gap-3 mb-4">
            <User className="h-5 w-5 text-primary" />
            <h2 className="text-xl font-semibold">Profile</h2>
          </div>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Full Name</label>
              <input
                type="text"
                defaultValue="John Doe"
                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Email</label>
              <input
                type="email"
                defaultValue="<EMAIL>"
                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              />
            </div>
            <button className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90">
              Save Changes
            </button>
          </div>
        </div>

        {/* Notification Settings */}
        <div className="bg-card p-6 rounded-lg border">
          <div className="flex items-center gap-3 mb-4">
            <Bell className="h-5 w-5 text-primary" />
            <h2 className="text-xl font-semibold">Notifications</h2>
          </div>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Email Notifications</p>
                <p className="text-sm text-muted-foreground">Receive email updates</p>
              </div>
              <input type="checkbox" defaultChecked className="h-4 w-4" />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Push Notifications</p>
                <p className="text-sm text-muted-foreground">Receive push notifications</p>
              </div>
              <input type="checkbox" defaultChecked className="h-4 w-4" />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">SMS Notifications</p>
                <p className="text-sm text-muted-foreground">Receive SMS updates</p>
              </div>
              <input type="checkbox" className="h-4 w-4" />
            </div>
          </div>
        </div>

        {/* Security Settings */}
        <div className="bg-card p-6 rounded-lg border">
          <div className="flex items-center gap-3 mb-4">
            <Shield className="h-5 w-5 text-primary" />
            <h2 className="text-xl font-semibold">Security</h2>
          </div>
          <div className="space-y-4">
            <button className="w-full text-left px-4 py-3 border rounded-md hover:bg-accent">
              Change Password
            </button>
            <button className="w-full text-left px-4 py-3 border rounded-md hover:bg-accent">
              Two-Factor Authentication
            </button>
            <button className="w-full text-left px-4 py-3 border rounded-md hover:bg-accent">
              Login Sessions
            </button>
          </div>
        </div>

        {/* Billing Settings */}
        <div className="bg-card p-6 rounded-lg border">
          <div className="flex items-center gap-3 mb-4">
            <CreditCard className="h-5 w-5 text-primary" />
            <h2 className="text-xl font-semibold">Billing</h2>
          </div>
          <div className="space-y-4">
            <div className="p-4 bg-accent/50 rounded-md">
              <p className="font-medium">Current Plan: Pro</p>
              <p className="text-sm text-muted-foreground">$29/month • Next billing: Jan 15, 2024</p>
            </div>
            <button className="w-full text-left px-4 py-3 border rounded-md hover:bg-accent">
              Manage Subscription
            </button>
            <button className="w-full text-left px-4 py-3 border rounded-md hover:bg-accent">
              Billing History
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
