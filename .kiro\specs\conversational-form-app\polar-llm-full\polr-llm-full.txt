# Create Benefit
Source: https://docs.polar.sh/api-reference/benefits/create

post /v1/benefits/
Create a benefit.

**Scopes**: `benefits:write`



# Delete Benefit
Source: https://docs.polar.sh/api-reference/benefits/delete

delete /v1/benefits/{id}
Delete a benefit.

> [!WARNING]
> Every grants associated with the benefit will be revoked.
> Users will lose access to the benefit.

**Scopes**: `benefits:write`



# Get Benefit
Source: https://docs.polar.sh/api-reference/benefits/get

get /v1/benefits/{id}
Get a benefit by ID.

**Scopes**: `benefits:read` `benefits:write`



# List Benefits
Source: https://docs.polar.sh/api-reference/benefits/list

get /v1/benefits/
List benefits.

**Scopes**: `benefits:read` `benefits:write`



# List Benefit Grants
Source: https://docs.polar.sh/api-reference/benefits/list-grants

get /v1/benefits/{id}/grants
List the individual grants for a benefit.

It's especially useful to check if a user has been granted a benefit.

**Scopes**: `benefits:read` `benefits:write`



# Update Benefit
Source: https://docs.polar.sh/api-reference/benefits/update

patch /v1/benefits/{id}
Update a benefit.

**Scopes**: `benefits:write`



# Create Checkout Link
Source: https://docs.polar.sh/api-reference/checkout-links/create

post /v1/checkout-links/
Create a checkout link.

**Scopes**: `checkout_links:write`

<Warning>
  Looking to create a single use checkout session? Checkout Links are probably **not** what you're looking for.

  Checkout Links are shareable links that generate checkout sessions when opened. They are very handy to start a purchase from your website or social media.

  However, if you want to start a checkout for one of your user **inside** your product, you should use the [Checkout Sessions API](/api-reference/checkouts/create-session).
</Warning>


# Delete Checkout Link
Source: https://docs.polar.sh/api-reference/checkout-links/delete

delete /v1/checkout-links/{id}
Delete a checkout link.

**Scopes**: `checkout_links:write`



# Get Checkout Link
Source: https://docs.polar.sh/api-reference/checkout-links/get

get /v1/checkout-links/{id}
Get a checkout link by ID.

**Scopes**: `checkout_links:read` `checkout_links:write`



# List Checkout Links
Source: https://docs.polar.sh/api-reference/checkout-links/list

get /v1/checkout-links/
List checkout links.

**Scopes**: `checkout_links:read` `checkout_links:write`



# Update Checkout Link
Source: https://docs.polar.sh/api-reference/checkout-links/update

patch /v1/checkout-links/{id}
Update a checkout link.

**Scopes**: `checkout_links:write`



# Confirm Checkout Session from Client
Source: https://docs.polar.sh/api-reference/checkouts/confirm-session-from-client

post /v1/checkouts/client/{client_secret}/confirm
Confirm a checkout session by client secret.

Orders and subscriptions will be processed.



# Create Checkout Session
Source: https://docs.polar.sh/api-reference/checkouts/create-session

post /v1/checkouts/
Create a checkout session.

**Scopes**: `checkouts:write`



# Get Checkout Session
Source: https://docs.polar.sh/api-reference/checkouts/get-session

get /v1/checkouts/{id}
Get a checkout session by ID.

**Scopes**: `checkouts:read` `checkouts:write`



# Get Checkout Session from Client
Source: https://docs.polar.sh/api-reference/checkouts/get-session-from-client

get /v1/checkouts/client/{client_secret}
Get a checkout session by client secret.



# List Checkout Sessions
Source: https://docs.polar.sh/api-reference/checkouts/list-sessions

get /v1/checkouts/
List checkout sessions.

**Scopes**: `checkouts:read` `checkouts:write`



# Update Checkout Session
Source: https://docs.polar.sh/api-reference/checkouts/update-session

patch /v1/checkouts/{id}
Update a checkout session.

**Scopes**: `checkouts:write`



# Update Checkout Session from Client
Source: https://docs.polar.sh/api-reference/checkouts/update-session-from-client

patch /v1/checkouts/client/{client_secret}
Update a checkout session by client secret.



# Create Custom Field
Source: https://docs.polar.sh/api-reference/custom-fields/create

post /v1/custom-fields/
Create a custom field.

**Scopes**: `custom_fields:write`



# Delete Custom Field
Source: https://docs.polar.sh/api-reference/custom-fields/delete

delete /v1/custom-fields/{id}
Delete a custom field.

**Scopes**: `custom_fields:write`



# Get Custom Field
Source: https://docs.polar.sh/api-reference/custom-fields/get

get /v1/custom-fields/{id}
Get a custom field by ID.

**Scopes**: `custom_fields:read` `custom_fields:write`



# List Custom Fields
Source: https://docs.polar.sh/api-reference/custom-fields/list

get /v1/custom-fields/
List custom fields.

**Scopes**: `custom_fields:read` `custom_fields:write`



# Update Custom Field
Source: https://docs.polar.sh/api-reference/custom-fields/update

patch /v1/custom-fields/{id}
Update a custom field.

**Scopes**: `custom_fields:write`



# Get Customer Meter
Source: https://docs.polar.sh/api-reference/customer-meters/get

get /v1/customer-meters/{id}
Get a customer meter by ID.

**Scopes**: `customer_meters:read`



# List Customer Meters
Source: https://docs.polar.sh/api-reference/customer-meters/list

get /v1/customer-meters/
List customer meters.

**Scopes**: `customer_meters:read`



# null
Source: https://docs.polar.sh/api-reference/customer-portal/downloadables/get

get /v1/customer-portal/downloadables/{token}



# List Downloadables
Source: https://docs.polar.sh/api-reference/customer-portal/downloadables/list

get /v1/customer-portal/downloadables/
**Scopes**: `customer_portal:read` `customer_portal:write`



# Get Customer
Source: https://docs.polar.sh/api-reference/customer-portal/get-customer

get /v1/customer-portal/customers/me
Get authenticated customer.

**Scopes**: `customer_portal:read` `customer_portal:write`



# Get Organization
Source: https://docs.polar.sh/api-reference/customer-portal/get-organization

get /v1/customer-portal/organizations/{slug}
Get a customer portal's organization by slug.



# Activate License Key
Source: https://docs.polar.sh/api-reference/customer-portal/license-keys/activate

post /v1/customer-portal/license-keys/activate
Activate a license key instance.

> This endpoint doesn't require authentication and can be safely used on a public
> client, like a desktop application or a mobile app.
> If you plan to validate a license key on a server, use the `/v1/license-keys/activate`
> endpoint instead.

<Tip>
  You only need to use this endpoint if you have device **activations** enabled on the license key benefit. You then use this endpoint to reserve an allocation for a specific device. Storing the unique activation ID from the response on the device and using it as extra validation in the [/validate](/api-reference/customer-portal/license-keys/validate) endpoint.

  Not using **activations**? Just use the [/validate](/api-reference/customer-portal/license-keys/validate) endpoint directly instead.
</Tip>


# Deactivate License Key
Source: https://docs.polar.sh/api-reference/customer-portal/license-keys/deactivate

post /v1/customer-portal/license-keys/deactivate
Deactivate a license key instance.

> This endpoint doesn't require authentication and can be safely used on a public
> client, like a desktop application or a mobile app.
> If you plan to validate a license key on a server, use the `/v1/license-keys/deactivate`
> endpoint instead.



# Get License Key
Source: https://docs.polar.sh/api-reference/customer-portal/license-keys/get

get /v1/customer-portal/license-keys/{id}
Get a license key.

**Scopes**: `customer_portal:read` `customer_portal:write`



# List License Keys
Source: https://docs.polar.sh/api-reference/customer-portal/license-keys/list

get /v1/customer-portal/license-keys/
**Scopes**: `customer_portal:read` `customer_portal:write`



# Validate License Key
Source: https://docs.polar.sh/api-reference/customer-portal/license-keys/validate

post /v1/customer-portal/license-keys/validate
Validate a license key.

> This endpoint doesn't require authentication and can be safely used on a public
> client, like a desktop application or a mobile app.
> If you plan to validate a license key on a server, use the `/v1/license-keys/validate`
> endpoint instead.



# Get Order
Source: https://docs.polar.sh/api-reference/customer-portal/orders/get

get /v1/customer-portal/orders/{id}
Get an order by ID for the authenticated customer.

**Scopes**: `customer_portal:read` `customer_portal:write`



# Get Order Invoice
Source: https://docs.polar.sh/api-reference/customer-portal/orders/get-invoice

get /v1/customer-portal/orders/{id}/invoice
Get an order's invoice data.

**Scopes**: `customer_portal:read` `customer_portal:write`

<Note>
  The invoice must be generated first before it can be retrieved. You should call the [`POST /v1/customer-portal/orders/{id}/invoice`](/api-reference/customer-portal/orders/post-invoice) endpoint to generate the invoice.

  If the invoice is not generated, you will receive a `404` error.
</Note>


# List Orders
Source: https://docs.polar.sh/api-reference/customer-portal/orders/list

get /v1/customer-portal/orders/
List orders of the authenticated customer.

**Scopes**: `customer_portal:read` `customer_portal:write`



# Update Order
Source: https://docs.polar.sh/api-reference/customer-portal/orders/patch

patch /v1/customer-portal/orders/{id}
Update an order for the authenticated customer.

**Scopes**: `customer_portal:write`



# Generate Order Invoice
Source: https://docs.polar.sh/api-reference/customer-portal/orders/post-invoice

post /v1/customer-portal/orders/{id}/invoice
Trigger generation of an order's invoice.

**Scopes**: `customer_portal:read` `customer_portal:write`

<Warning>
  Once the invoice is generated, it's permanent and cannot be modified.

  Make sure the billing details (name and address) are correct before generating the invoice. You can update them before generating the invoice by calling the [`PATCH /v1/customer-portal/orders/{id}`](/api-reference/customer-portal/orders/patch) endpoint.
</Warning>

<Note>
  After successfully calling this endpoint, you get a `202` response, meaning
  the generation of the invoice has been scheduled. It usually only takes a few
  seconds before you can retrieve the invoice using the [`GET /v1/customer-portal/orders/{id}/invoice`](/api-reference/customer-portal/orders/get-invoice) endpoint.

  If you want a reliable notification when the invoice is ready, you can listen to the
  [`order.updated`](/api-reference/webhooks/order.updated) webhook and check the [`is_invoice_generated` field](/api-reference/webhooks/order.updated#schema-data-is-invoice-generated).
</Note>


# Create Customer Session
Source: https://docs.polar.sh/api-reference/customer-portal/sessions/create

post /v1/customer-sessions/
Create a customer session.

**Scopes**: `customer_sessions:write`



# Cancel Subscription
Source: https://docs.polar.sh/api-reference/customer-portal/subscriptions/cancel

delete /v1/customer-portal/subscriptions/{id}
Cancel a subscription of the authenticated customer.

**Scopes**: `customer_portal:write`



# Get Subscription
Source: https://docs.polar.sh/api-reference/customer-portal/subscriptions/get

get /v1/customer-portal/subscriptions/{id}
Get a subscription for the authenticated customer.

**Scopes**: `customer_portal:read` `customer_portal:write`



# List Subscriptions
Source: https://docs.polar.sh/api-reference/customer-portal/subscriptions/list

get /v1/customer-portal/subscriptions/
List subscriptions of the authenticated customer.

**Scopes**: `customer_portal:read` `customer_portal:write`



# Update Subscription
Source: https://docs.polar.sh/api-reference/customer-portal/subscriptions/update

patch /v1/customer-portal/subscriptions/{id}
Update a subscription of the authenticated customer.

**Scopes**: `customer_portal:write`



# Create Customer
Source: https://docs.polar.sh/api-reference/customers/create

post /v1/customers/
Create a customer.

**Scopes**: `customers:write`



# Delete Customer
Source: https://docs.polar.sh/api-reference/customers/delete

delete /v1/customers/{id}
Delete a customer.

This action cannot be undone and will immediately:
- Cancel any active subscriptions for the customer
- Revoke all their benefits
- Clear any `external_id`

Use it only in the context of deleting a user within your
own service. Otherwise, use more granular API endpoints to cancel
a specific subscription or revoke certain benefits.

Note: The customers information will nonetheless be retained for historic
orders and subscriptions.

**Scopes**: `customers:write`



# Delete Customer by External ID
Source: https://docs.polar.sh/api-reference/customers/delete-external

delete /v1/customers/external/{external_id}
Delete a customer by external ID.

Immediately cancels any active subscriptions and revokes any active benefits.

**Scopes**: `customers:write`



# Get Customer
Source: https://docs.polar.sh/api-reference/customers/get

get /v1/customers/{id}
Get a customer by ID.

**Scopes**: `customers:read` `customers:write`



# Get Customer by External ID
Source: https://docs.polar.sh/api-reference/customers/get-external

get /v1/customers/external/{external_id}
Get a customer by external ID.

**Scopes**: `customers:read` `customers:write`



# List Customers
Source: https://docs.polar.sh/api-reference/customers/list

get /v1/customers/
List customers.

**Scopes**: `customers:read` `customers:write`



# Get Customer State
Source: https://docs.polar.sh/api-reference/customers/state

get /v1/customers/{id}/state
Get a customer state by ID.

The customer state includes information about
the customer's active subscriptions and benefits.

It's the ideal endpoint to use when you need to get a full overview
of a customer's status.

**Scopes**: `customers:read` `customers:write`



# Get Customer State by External ID
Source: https://docs.polar.sh/api-reference/customers/state-external

get /v1/customers/external/{external_id}/state
Get a customer state by external ID.

The customer state includes information about
the customer's active subscriptions and benefits.

It's the ideal endpoint to use when you need to get a full overview
of a customer's status.

**Scopes**: `customers:read` `customers:write`



# Update Customer
Source: https://docs.polar.sh/api-reference/customers/update

patch /v1/customers/{id}
Update a customer.

**Scopes**: `customers:write`



# Update Customer by External ID
Source: https://docs.polar.sh/api-reference/customers/update-external

patch /v1/customers/external/{external_id}
Update a customer by external ID.

**Scopes**: `customers:write`



# Create Discount
Source: https://docs.polar.sh/api-reference/discounts/create

post /v1/discounts/
Create a discount.

**Scopes**: `discounts:write`



# Delete Discount
Source: https://docs.polar.sh/api-reference/discounts/delete

delete /v1/discounts/{id}
Delete a discount.

**Scopes**: `discounts:write`



# Get Discount
Source: https://docs.polar.sh/api-reference/discounts/get

get /v1/discounts/{id}
Get a discount by ID.

**Scopes**: `discounts:read` `discounts:write`



# List Discounts
Source: https://docs.polar.sh/api-reference/discounts/list

get /v1/discounts/
List discounts.

**Scopes**: `discounts:read` `discounts:write`



# Update Discount
Source: https://docs.polar.sh/api-reference/discounts/update

patch /v1/discounts/{id}
Update a discount.

**Scopes**: `discounts:write`



# Get Event
Source: https://docs.polar.sh/api-reference/events/get

get /v1/events/{id}
Get an event by ID.

**Scopes**: `events:read` `events:write`



# Ingest Events
Source: https://docs.polar.sh/api-reference/events/ingest

post /v1/events/ingest
Ingest batch of events.

**Scopes**: `events:write`



# List Events
Source: https://docs.polar.sh/api-reference/events/list

get /v1/events/
List events.

**Scopes**: `events:read` `events:write`



# Complete File Upload
Source: https://docs.polar.sh/api-reference/files/complete-upload

post /v1/files/{id}/uploaded
Complete a file upload.

**Scopes**: `files:write`



# Create File
Source: https://docs.polar.sh/api-reference/files/create

post /v1/files/
Create a file.

**Scopes**: `files:write`



# Delete File
Source: https://docs.polar.sh/api-reference/files/delete

delete /v1/files/{id}
Delete a file.

**Scopes**: `files:write`



# List Files
Source: https://docs.polar.sh/api-reference/files/list

get /v1/files/
List files.

**Scopes**: `files:read` `files:write`



# Update File
Source: https://docs.polar.sh/api-reference/files/update

patch /v1/files/{id}
Update a file.

**Scopes**: `files:write`



# API Overview
Source: https://docs.polar.sh/api-reference/introduction

Base URLs, authentication (OAT), and the difference between the Core API and the Customer Portal API

## TL;DR

<CardGroup cols={2}>
  <Card title="Production Base URL" icon="globe">
    `https://api.polar.sh/v1`
  </Card>

  <Card title="Sandbox Base URL" icon="flask">
    `https://sandbox-api.polar.sh/v1`
  </Card>

  <Card title="Auth (Organization)" icon="key" href="/integrate/oat">
    Use an **Organization Access Token (OAT)** in the `Authorization: Bearer`
    header
  </Card>

  <Card title="Auth (Customer Portal)" icon="user-lock" href="/api-reference/customer-portal/sessions/create">
    Use a **Customer Access Token** created via `/v1/customer-sessions/`
  </Card>
</CardGroup>

## Base URLs

| Environment | Base URL                          | Purpose                         |
| ----------- | --------------------------------- | ------------------------------- |
| Production  | `https://api.polar.sh/v1`         | Real customers & live payments  |
| Sandbox     | `https://sandbox-api.polar.sh/v1` | Safe testing & integration work |

<Info>
  The sandbox environment is fully isolated—data, users, tokens, and
  organizations created there do not affect production. Create separate tokens
  in each environment.
</Info>

Read more: [Sandbox Environment](/integrate/sandbox)

## Authentication

### Organization Access Tokens (OAT)

Use an **OAT** to act on behalf of your organization (manage products, prices, checkouts, orders, subscriptions, benefits, etc.).

```http
Authorization: Bearer polar_oat_xxxxxxxxxxxxxxxxx
```

<Tip>
  Create OATs in your organization settings. See: [Organization Access
  Tokens](/integrate/oat)
</Tip>

<Warning>
  Never expose an OAT in client-side code, public repos, or logs. If leaked, it
  will be revoked automatically by our secret scanning integrations.
</Warning>

### Customer Access Tokens

Do **not** use OATs in the browser. For customer-facing flows, [generate a **Customer Session**](/api-reference/customer-portal/sessions/create) server-side, then use the returned **customer access token** with the **Customer Portal API** to let a signed-in customer view their own orders, subscriptions, and benefits.

## Core API vs Customer Portal API

| Aspect               | Core API                                                                 | Customer Portal API                            |
| -------------------- | ------------------------------------------------------------------------ | ---------------------------------------------- |
| Audience             | Your server / backend                                                    | One of your customer                           |
| Auth Type            | Organization Access Token (OAT)                                          | Customer Access Token                          |
| Scope                | Full org resources (products, orders, subscriptions, benefits, checkout) | Only the authenticated customer’s data         |
| Typical Use          | Admin dashboards, internal tools, automation, provisioning               | Building a custom customer portal or gated app |
| Token Creation       | Via dashboard (manual)                                                   | Via `/v1/customer-sessions/` (server-side)     |
| Sensitive Operations | Yes (create/update products, issue refunds, etc.)                        | No (read/update only what the customer owns)   |

<Note>
  The Customer Portal API is a *restricted* surface designed for safe exposure
  in user-facing contexts (after exchanging a session). It cannot perform
  privileged org-level mutations like creating products or issuing refunds.
</Note>

## Quick Examples

<CodeGroup>
  ```bash curl (Production - Core API)
  curl https://api.polar.sh/v1/products/ \
    -H "Authorization: Bearer $POLAR_OAT" \
    -H "Accept: application/json"
  ```

  ```bash curl (Sandbox - Core API)
  curl https://sandbox-api.polar.sh/v1/products/ \
    -H "Authorization: Bearer $POLAR_OAT_SANDBOX" \
    -H "Accept: application/json"
  ```

  ```bash curl (Customer Portal API)
  curl https://api.polar.sh/v1/customer-portal/orders/ \
    -H "Authorization: Bearer $POLAR_CUSTOMER_TOKEN" \
    -H "Accept: application/json"
  ```
</CodeGroup>

## Using SDKs

All official SDKs accept a `server` parameter for sandbox usage:

<CodeGroup>
  ```ts TypeScript
  import { Polar } from "@polar-sh/sdk";

  const polar = new Polar({
  accessToken: process.env.POLAR_ACCESS_TOKEN!,
  server: "sandbox", // omit or use 'production' for live
  });

  ```

  ```py Python
  from polar import Polar

  client = Polar(
      access_token=os.environ["POLAR_ACCESS_TOKEN"],
      server="sandbox",
  )
  ```

  ```go Go
  s := polargo.New(
    polargo.WithServer("sandbox"),
    polargo.WithSecurity(os.Getenv("POLAR_ACCESS_TOKEN")),
  )
  ```

  ```php PHP
  $sdk = Polar\Polar::builder()
      ->setServer('sandbox')
      ->setSecurity(getenv('POLAR_ACCESS_TOKEN'))
      ->build();
  ```
</CodeGroup>

## Rate Limits

Polar API has rate limits to ensure fair usage and maintain performance. The limits are as follows:

* **300 requests per minute** per organization/customer or OAuth2 Client.
* **1 request per second** for unauthenticated license key [validation](/api-reference/customer-portal/license-keys/validate), [activation](/api-reference/customer-portal/license-keys/activate), and [deactivation](/api-reference/customer-portal/license-keys/deactivate) endpoints.

If you exceed the rate limit, you will receive a `429 Too Many Requests` response. The response will include a `Retry-After` header indicating how long you should wait before making another request.

<Note>
  Organizations requiring higher rate limits for production workloads may
  contact our support team to discuss elevated limits.
</Note>


# Activate License Key
Source: https://docs.polar.sh/api-reference/license-keys/activate

post /v1/license-keys/activate
Activate a license key instance.

**Scopes**: `license_keys:write`



# Deactivate License Key
Source: https://docs.polar.sh/api-reference/license-keys/deactivate

post /v1/license-keys/deactivate
Deactivate a license key instance.

**Scopes**: `license_keys:write`



# Get License Key
Source: https://docs.polar.sh/api-reference/license-keys/get

get /v1/license-keys/{id}
Get a license key.

**Scopes**: `license_keys:read` `license_keys:write`



# Get Activation
Source: https://docs.polar.sh/api-reference/license-keys/get-activation

get /v1/license-keys/{id}/activations/{activation_id}
Get a license key activation.

**Scopes**: `license_keys:read` `license_keys:write`



# List License Keys
Source: https://docs.polar.sh/api-reference/license-keys/list

get /v1/license-keys/
Get license keys connected to the given organization & filters.

**Scopes**: `license_keys:read` `license_keys:write`



# Update License Key
Source: https://docs.polar.sh/api-reference/license-keys/update

patch /v1/license-keys/{id}
Update a license key.

**Scopes**: `license_keys:write`



# Validate License Key
Source: https://docs.polar.sh/api-reference/license-keys/validate

post /v1/license-keys/validate
Validate a license key.

**Scopes**: `license_keys:write`



# Create Meter
Source: https://docs.polar.sh/api-reference/meters/create

post /v1/meters/
Create a meter.

**Scopes**: `meters:write`



# Get Meter
Source: https://docs.polar.sh/api-reference/meters/get

get /v1/meters/{id}
Get a meter by ID.

**Scopes**: `meters:read` `meters:write`



# Get Meter Quantities
Source: https://docs.polar.sh/api-reference/meters/get-quantities

get /v1/meters/{id}/quantities
Get quantities of a meter over a time period.

**Scopes**: `meters:read` `meters:write`



# List Meters
Source: https://docs.polar.sh/api-reference/meters/list

get /v1/meters/
List meters.

**Scopes**: `meters:read` `meters:write`



# Update Meter
Source: https://docs.polar.sh/api-reference/meters/update

patch /v1/meters/{id}
Update a meter.

**Scopes**: `meters:write`



# Get Metrics
Source: https://docs.polar.sh/api-reference/metrics/get

get /v1/metrics/
Get metrics about your orders and subscriptions.

Currency values are output in cents.

**Scopes**: `metrics:read`



# Get Metrics Limits
Source: https://docs.polar.sh/api-reference/metrics/get-limits

get /v1/metrics/limits
Get the interval limits for the metrics endpoint.

**Scopes**: `metrics:read`



# Authorize
Source: https://docs.polar.sh/api-reference/oauth2/connect/authorize

get /v1/oauth2/authorize



# Get User Info
Source: https://docs.polar.sh/api-reference/oauth2/connect/get-user-info

get /v1/oauth2/userinfo
Get information about the authenticated user.



# Introspect Token
Source: https://docs.polar.sh/api-reference/oauth2/connect/introspect-token

post /v1/oauth2/introspect
Get information about an access token.



# Request Token
Source: https://docs.polar.sh/api-reference/oauth2/connect/request-token

post /v1/oauth2/token
Request an access token using a valid grant.



# Revoke Token
Source: https://docs.polar.sh/api-reference/oauth2/connect/revoke-token

post /v1/oauth2/revoke
Revoke an access token or a refresh token.



# Get Order
Source: https://docs.polar.sh/api-reference/orders/get

get /v1/orders/{id}
Get an order by ID.

**Scopes**: `orders:read`



# Get Order Invoice
Source: https://docs.polar.sh/api-reference/orders/get-invoice

get /v1/orders/{id}/invoice
Get an order's invoice data.

**Scopes**: `orders:read`

<Note>
  The invoice must be generated first before it can be retrieved. You should call the [`POST /v1/orders/{id}/invoice`](/api-reference/orders/post-invoice) endpoint to generate the invoice.

  If the invoice is not generated, you will receive a `404` error.
</Note>


# List Orders
Source: https://docs.polar.sh/api-reference/orders/list

get /v1/orders/
List orders.

**Scopes**: `orders:read`



# Update Order
Source: https://docs.polar.sh/api-reference/orders/patch

patch /v1/orders/{id}
Update an order.

**Scopes**: `orders:write`



# Generate Order Invoice
Source: https://docs.polar.sh/api-reference/orders/post-invoice

post /v1/orders/{id}/invoice
Trigger generation of an order's invoice.

**Scopes**: `orders:read`

<Warning>
  Once the invoice is generated, it's permanent and cannot be modified.

  Make sure the billing details (name and address) are correct before generating the invoice. You can update them before generating the invoice by calling the [`PATCH /v1/orders/{id}`](/api-reference/orders/patch) endpoint.
</Warning>

<Note>
  After successfully calling this endpoint, you get a `202` response, meaning
  the generation of the invoice has been scheduled. It usually only takes a few
  seconds before you can retrieve the invoice using the [`GET /v1/orders/{id}
    /invoice`](/api-reference/orders/get-invoice) endpoint.

  If you want a reliable notification when the invoice is ready, you can listen to the
  [`order.updated`](/api-reference/webhooks/order.updated) webhook and check the [`is_invoice_generated` field](/api-reference/webhooks/order.updated#schema-data-is-invoice-generated).
</Note>


# Create Organization
Source: https://docs.polar.sh/api-reference/organizations/create

post /v1/organizations/
Create an organization.

**Scopes**: `organizations:write`



# Get Organization
Source: https://docs.polar.sh/api-reference/organizations/get

GET /v1/organizations/{id}
Get an organization by ID.

**Scopes**: `organizations:read` `organizations:write`

Hello there. Just testing a custom intro.

| Property | Description                           |
| -------- | ------------------------------------- |
| Name     | Full name of user                     |
| Age      | Reported age                          |
| Joined   | Whether the user joined the community |

Continuing here.

<Update label="2024-10-12" description="v0.1.1">
  Some update to the endpoint here
</Update>


# List Organizations
Source: https://docs.polar.sh/api-reference/organizations/list

get /v1/organizations/
List organizations.

**Scopes**: `organizations:read` `organizations:write`



# Update Organization
Source: https://docs.polar.sh/api-reference/organizations/update

patch /v1/organizations/{id}
Update an organization.

**Scopes**: `organizations:write`



# Create Product
Source: https://docs.polar.sh/api-reference/products/create

post /v1/products/
Create a product.

**Scopes**: `products:write`



# Get Product
Source: https://docs.polar.sh/api-reference/products/get

get /v1/products/{id}
Get a product by ID.

**Scopes**: `products:read` `products:write`



# List Products
Source: https://docs.polar.sh/api-reference/products/list

get /v1/products/
List products.

**Scopes**: `products:read` `products:write`



# Update Product
Source: https://docs.polar.sh/api-reference/products/update

patch /v1/products/{id}
Update a product.

**Scopes**: `products:write`



# Update Product Benefits
Source: https://docs.polar.sh/api-reference/products/update-benefits

post /v1/products/{id}/benefits
Update benefits granted by a product.

**Scopes**: `products:write`



# Create Refund
Source: https://docs.polar.sh/api-reference/refunds/create

post /v1/refunds/
Create a refund.

**Scopes**: `refunds:write`



# List Refunds
Source: https://docs.polar.sh/api-reference/refunds/list

get /v1/refunds/
List products.

**Scopes**: `refunds:read` `refunds:write`



# Get Subscription
Source: https://docs.polar.sh/api-reference/subscriptions/get

get /v1/subscriptions/{id}
Get a subscription by ID.

**Scopes**: `subscriptions:read` `subscriptions:write`



# List Subscriptions
Source: https://docs.polar.sh/api-reference/subscriptions/list

get /v1/subscriptions/
List subscriptions.

**Scopes**: `subscriptions:read` `subscriptions:write`



# Revoke Subscription
Source: https://docs.polar.sh/api-reference/subscriptions/revoke

delete /v1/subscriptions/{id}
Revoke a subscription, i.e cancel immediately.

**Scopes**: `subscriptions:write`



# Update Subscription
Source: https://docs.polar.sh/api-reference/subscriptions/update

patch /v1/subscriptions/{id}
Update a subscription.

**Scopes**: `subscriptions:write`



# benefit.created
Source: https://docs.polar.sh/api-reference/webhooks/benefit.created





# benefit.updated
Source: https://docs.polar.sh/api-reference/webhooks/benefit.updated





# benefit_grant.created
Source: https://docs.polar.sh/api-reference/webhooks/benefit_grant.created





# benefit_grant.cycled
Source: https://docs.polar.sh/api-reference/webhooks/benefit_grant.cycled





# benefit_grant.revoked
Source: https://docs.polar.sh/api-reference/webhooks/benefit_grant.revoked





# benefit_grant.updated
Source: https://docs.polar.sh/api-reference/webhooks/benefit_grant.updated





# checkout.created
Source: https://docs.polar.sh/api-reference/webhooks/checkout.created





# checkout.updated
Source: https://docs.polar.sh/api-reference/webhooks/checkout.updated





# customer.created
Source: https://docs.polar.sh/api-reference/webhooks/customer.created





# customer.deleted
Source: https://docs.polar.sh/api-reference/webhooks/customer.deleted





# customer.state_changed
Source: https://docs.polar.sh/api-reference/webhooks/customer.state_changed





# customer.updated
Source: https://docs.polar.sh/api-reference/webhooks/customer.updated





# Create Webhook Endpoint
Source: https://docs.polar.sh/api-reference/webhooks/endpoints/create

post /v1/webhooks/endpoints
Create a webhook endpoint.

**Scopes**: `webhooks:write`



# Delete Webhook Endpoint
Source: https://docs.polar.sh/api-reference/webhooks/endpoints/delete

delete /v1/webhooks/endpoints/{id}
Delete a webhook endpoint.

**Scopes**: `webhooks:write`



# Get Webhook Endpoint
Source: https://docs.polar.sh/api-reference/webhooks/endpoints/get

get /v1/webhooks/endpoints/{id}
Get a webhook endpoint by ID.

**Scopes**: `webhooks:read` `webhooks:write`



# List Webhook Endpoints
Source: https://docs.polar.sh/api-reference/webhooks/endpoints/list

get /v1/webhooks/endpoints
List webhook endpoints.

**Scopes**: `webhooks:read` `webhooks:write`



# Update Webhook Endpoint
Source: https://docs.polar.sh/api-reference/webhooks/endpoints/update

patch /v1/webhooks/endpoints/{id}
Update a webhook endpoint.

**Scopes**: `webhooks:write`



# order.created
Source: https://docs.polar.sh/api-reference/webhooks/order.created





# order.paid
Source: https://docs.polar.sh/api-reference/webhooks/order.paid





# order.refunded
Source: https://docs.polar.sh/api-reference/webhooks/order.refunded





# order.updated
Source: https://docs.polar.sh/api-reference/webhooks/order.updated





# organization.updated
Source: https://docs.polar.sh/api-reference/webhooks/organization.updated





# product.created
Source: https://docs.polar.sh/api-reference/webhooks/product.created





# product.updated
Source: https://docs.polar.sh/api-reference/webhooks/product.updated





# refund.created
Source: https://docs.polar.sh/api-reference/webhooks/refund.created





# refund.updated
Source: https://docs.polar.sh/api-reference/webhooks/refund.updated





# subscription.active
Source: https://docs.polar.sh/api-reference/webhooks/subscription.active





# subscription.canceled
Source: https://docs.polar.sh/api-reference/webhooks/subscription.canceled





# subscription.created
Source: https://docs.polar.sh/api-reference/webhooks/subscription.created





# subscription.revoked
Source: https://docs.polar.sh/api-reference/webhooks/subscription.revoked





# subscription.uncanceled
Source: https://docs.polar.sh/api-reference/webhooks/subscription.uncanceled





# subscription.updated
Source: https://docs.polar.sh/api-reference/webhooks/subscription.updated





# API Changelog
Source: https://docs.polar.sh/changelog/api

Stay up to date with the latest changes, improvements and deprecations to the Polar API.

<Update label="2025-06-18">
  ## Checkout API and Customer Session API changes

  To be more consistent across our API, we've renamed `customer_external_id` field to `external_customer_id` in the Checkout API and Customer Session API.

  * <Icon icon="circle-exclamation" size={16} color="orange" /> **Deprecated**:
    `customer_external_id` field in the Checkout API and Customer Session API. Use
    `external_customer_id` instead.

  ## Benefit metadata in Customer State

  The customer state now includes the [benefit metadata](/api-reference/customers/state#response-benefit-metadata) in the `granted_benefits` list.
</Update>

<Update label="2025-06-17">
  ## Webhook API endpoints are now documented

  The API endpoints for managing webhooks are now documented in the API reference, and fully supported in our SDK.

  [Read more](/api-reference/webhooks/endpoints/create)
</Update>

<Update label="2025-06-05">
  ## Rate limits

  To ensure fair usage and maintain performance, we've introduced rate limits for the API. The limits are as follows:

  * **100 requests per second** per IP address.
</Update>

<Update label="2025-06-02">
  ## Order invoice generation and retrieval

  Until now, the invoice was generated automatically when the order was created, allowing you to call [`GET /v1/orders/{id}/invoice`](/api-reference/orders/get-invoice) and [`GET /v1/customer-portal/orders/{id}/invoice`](/api-reference/customer-portal/orders/get-invoice) endpoints without any prior action.

  We now require you to explicitly generate the invoice by calling the [`POST /v1/orders/{id}/invoice`](/api-reference/orders/post-invoice) or [`POST /v1/customer-portal/orders/{id}/invoice`](/api-reference/customer-portal/orders/post-invoice) endpoints.

  This change allows us to better handle the invoice generation process, and to allow the customer to change the billing details (name and address) before generating the invoice. This can be done through the [`PATCH /v1/orders/{id}`](/api-reference/orders/patch) or [`PATCH /v1/customer-portal/orders/{id}`](/api-reference/customer-portal/orders/patch) endpoints.
</Update>

<Update label="2025-04-16">
  ## Benefit metadata support and floating point numbers in metadata

  * <Icon icon="check" size={16} color="green" /> **Added**: Benefits now support
    [metadata](/api-reference/benefits/create#body-metadata).
  * <Icon icon="check" size={16} color="green" /> **Added**: Metadata values now
    support floating-point numbers. Before, only strings, integers and booleans
    were supported.
</Update>

<Update label="2025-03-25">
  ## Checkout amount fields changes and depreciations

  To be more consistent with the [Order schema changes](#2025-03-14), we've made some changes to the field related to amounts in the Checkout schema.

  * <Icon icon="check" size={16} color="green" /> **Added**:
    [`checkout.discount_amount`](/api-reference/checkouts/get-session#response-discount-amount).
  * <Icon icon="check" size={16} color="green" /> **Added**:
    [`checkout.net_amount`](/api-reference/checkouts/get-session#response-net-amount).
  * <Icon icon="circle-exclamation" size={16} color="orange" /> **Deprecated**:
    `checkout.subtotal_amount`, use
    [`checkout.net_amount`](/api-reference/checkouts/get-session#response-net-amount)
    instead.
</Update>

<Update label="2025-03-20">
  ## New order status and webhooks

  Until now, Polar only kept track of fully processed, **paid** orders. To help you keep track of the order lifecycle, we've added a new status `pending`, which is a transitive state meaning the order is created but not paid yet. In most cases, the order will transition from `pending` to `paid` in a short time.

  * <Icon icon="circle-exclamation" size={16} color="orange" /> When receiving
    `order.created` event, the order status might not be `paid`.
  * <Icon icon="check" size={16} color="green" /> **Added**:
    [`order.updated`](/api-reference/webhooks/order.updated) webhook, sent when
    the order status changes or when it's partially or fully refunded.
  * <Icon icon="check" size={16} color="green" /> **Added**:
    [`order.paid`](/api-reference/webhooks/order.paid) webhook, sent when the
    order is fully processed and paid.
  * <Icon icon="check" size={16} color="green" /> **Added**:
    [`Order.paid`](/api-reference/orders/get#response-paid) property to the order
    schema.

  <Info>
    If you were relying on the `order.created` webhook to keep track of succesful
    orders, we recommend you to switch to `order.paid`.
  </Info>
</Update>

<Update label="2025-03-14">
  ## Subscriptions and Orders schema changes

  To prepare our next move to support usage-based billing, we've made some changes to the [`Subscription`](/api-reference/subscriptions/get) and [`Order`](/api-reference/orders/get) schemas. The main reason behind those is that we need to support multiple prices and items in a single subscription or order.

  * <Icon icon="circle-exclamation" size={16} color="orange" /> **Deprecated**:
    `Subscription.price_id` and `Subscription.price`. Use the
    `Subscription.prices` array instead.
  * <Icon icon="circle-exclamation" size={16} color="orange" /> **Deprecated**:
    `Order.product_price_id` and `Order.product_price`. Use the `Order.items`
    array instead.
  * <Icon icon="circle-exclamation" size={16} color="orange" /> **Deprecated**:
    `Order.amount`. Use the `Order.net_amount` instead. It has the same value and
    meaning, but the new name is more explicit.
  * <Icon icon="check" size={16} color="green" /> **Added**:
    `Order.subtotal_amount`, `Order.discount_amount`, and `Order.total_amount`
    fields to provide a more detailed breakdown of the order amount.
</Update>


# Product Updates
Source: https://docs.polar.sh/changelog/recent

Stay up to date with the latest changes and improvements to Polar.

<Update label="2025-06-12">
  ## Update subscription discount

  We've added the ability to update the discount on a subscription. This allows you to add, remove, or change the discount applied to a subscription at any time.

  This feature is both available through the [API](/api-reference/subscriptions/update) and the dashboard.
</Update>

<Update label="2025-06-05">
  ## Payout Reverse Invoices

  We've added the ability to generate reverse invoices for payouts directly from the Payouts page. This feature allows you to easily create an invoice that details the sales made on your behalf, minus our fees.

  [Read more](/features/finance/payouts#reverse-invoices)
</Update>

<Update label="2025-05-22">
  ## Business Purchase Option on Checkout

  We've added a new "I'm purchasing as a business" checkbox to the Checkout flow. When selected, customers are required to provide their business billing name and complete billing address.
</Update>

<Update label="2025-05-19">
  ## Enhanced Attribution for Checkout Links

  We've added support for `reference_id` and UTM parameters (`utm_source`, `utm_medium`, `utm_campaign`, `utm_content`, `utm_term`) as query parameters for Checkout Links. These parameters are automatically stored in the Checkout metadata, allowing you to track the source of your traffic and conversions more effectively.

  [Read more](/features/checkout/links#store-attribution-and-reference-metadata)
</Update>

<Update label="2025-05-15">
  ## Checkouts and payments insights

  We've added a new **Checkouts** tab under the **Sales**, where you can review all the checkout sessions, successful or not. You can filter them by customer email, status, and product. You can also see the payment attempts for each checkout session, including the reason for any failed or declined payments.

  <img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/orders/checkout.light.png" />

  <img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/orders/checkout.dark.png" />

  The payment attempts information is also available on each order.

  Besides, we've also added new analytics around checkouts: total number of checkouts, successful checkouts, and conversion rate.
</Update>

<Update label="2025-03-11">
  ## Zapier integration officially launched

  We're excited to announce the official launch of our [Zapier integration](https://zapier.com/apps/polar/integrations)! Get started now and connect Polar to 2,000+ other web services.

  <Note>
    We've focused on **triggers** (webhooks) for now, so you can react to events in Polar and trigger actions in other apps.

    Need to perform actions in Polar? Tell us about your use case [here](https://github.com/orgs/polarsource/discussions/new?category=integrations\&labels=integrations%2Fzapier) and we'll consider adding more actions in the future.
  </Note>
</Update>

<Update label="2025-03-05">
  ## Customer State

  Maybe one of our neatest features to date! Customer State is a concept allowing you to query for the current state of a customer, including their **active subscriptions** and **granted [benefits](/features/benefits/introduction)**, in a single [API call](/api-reference/customers/state-external) or single [webhook event](/api-reference/webhooks/customer.state_changed).

  Combined with the [External ID](/features/customer-management#external-id) feature, you can get up-and-running in minutes.

  [Read more](/integrate/customer-state)
</Update>

<Update label="2025-03-04">
  ## Better Auth Plugin

  Integrating authentication and billing for your users has never been easier.

  <img src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/changelog/2025-03-04/better_auth.jpeg" />

  [Better Auth](https://www.better-auth.com/) is an open source authentication framework for
  TypeScript that is quickly becoming a favorite amongst developers. Today, we're
  thrilled to have shipped a Polar plugin for Better Auth - in collaboration with them.

  Checkout our [integration guide](/integrate/sdk/adapters/better-auth).
</Update>

<Update label="2025-02-27">
  ## Customer External ID

  We've added support for an `external_id` field on Customers. We believe this will greatly simplify the reconciliation process between your system and Polar.

  Previously, the recommended way to reconcile with your users was to use `metadata`. However, this was not always the most convenient method, especially if you needed to fetch a Customer from our API.

  With `external_id`, you can now fetch a Customer directly by their external ID through dedicated `GET`, `PATCH`, and `DELETE` endpoints. You don't even need to store Polar's internal ID in your system anymore! [Read more](/features/customer-management#external-id)

  Of course, you can also directly preset `external_customer_id` when creating a Checkout Session, and it will automatically be set on the newly created Customer after a successful checkout. [Read more](/features/checkout/session#external-customer-id)
</Update>

<Update label="2025-02-19">
  ## Polar's take on Product variants

  We've released big changes to how we handle products and pricing, allowing us to support a unique approach to what the industry typically calls **variants** 🔥

  We believe having a single product with multiple pricing models and benefits adds unneccessary complexity to the user and to the API. Instead, we chose to treat everything as a product, giving you maximum flexibility about the pricing and benefits you want to offer.

  Thus, we introduce support for **multiple products** at checkout, allowing customers to switch between them before purchasing. Typically, you can offer a monthly and a yearly product, with specific pricing and benefits for each.

  {" "}

  <img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/checkout/session/checkout_multiple_products.light.png" />

  <img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/checkout/session/checkout_multiple_products.dark.png" />

  This is available right now using the [Checkout Session API](/features/checkout/session) and [Checkout Links](/features/checkout/links).

  ### Depreciations

  * Products can no longer have both a monthly and yearly pricing. Existing products still work, but you'll see a warning like this when trying to edit their pricing:

  {" "}

  <Frame>
    <img className="block dark:hidden h-[200px]" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/changelog/2025-02-19/deprecated_pricing.light.png" />

    <img className="hidden dark:block h-[200px]" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/changelog/2025-02-19/deprecated_pricing.dark.png" />
  </Frame>

  ### API changes

  * The `product_id` and `product_price_id` fields are deprecated in the [Checkout Session API](/api-reference/checkouts/create-session). You should now use the `products` field to specify the products you want to include in the checkout.
  * The `type` and `recurring_interval` fields on `ProductPrice` are deprecated. `recurring_interval` is now set directly on `Product`.
</Update>

<Update label="2025-02-14">
  New documentation platform. More improvements incoming.
</Update>


# Analytics
Source: https://docs.polar.sh/features/analytics



## Sales Metrics

Polar offers a professional metrics dashboard out of the box. So you can stay focused on increasing revenue vs. how to measure it.

<img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/analytics/overview.light.png" />

<img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/analytics/overview.dark.png" />

**Missing any metrics?** [Let us know so we can add it.](https://github.com/orgs/polarsource/discussions/categories/feature-requests)

### Filters

You can easily slice and dice metrics with the filters below.

### Period

Change the time period in the X-axis to one of:

* Yearly
* Monthly
* Weekly
* Daily
* Hourly

### Timeframe

You can choose a date range to view all metrics for.

### Product

By default metrics reflect the total across all products. However, you can specify individual products or subscription tiers to filter metrics by.

## Metrics

### Revenue

How much revenue you've earned before fees.

### Orders

How many product sales and subscription payments have been made.

### Average Order Value (AOV)

The average earning per order, i.e revenue / orders.

### One-Time Products

Amount of products sold.

### One-Time Products Revenue

Amount of revenue earned from products.

### New Subscriptions

Amount of new subscriptions.

### New Subscription Revenue

Amount of revenue earned from new subscriptions.

### Renewed Subscriptions

Amount of renewed subscriptions.

### Renewed Subscription Revenue

Amount of revenue earned from renewed subscriptions.

### Active Subscriptions

Amount of active subscriptions (new + renewed)

### Monthly Recurring Revenue (MRR)

Amount of revenue earned from active subscriptions.

### Checkouts

Number of created checkouts.

### Succeeded Checkouts

Number of successful checkouts, i.e. checkouts that lead to a new order or subscription.

### Checkouts Conversion Rate

The percentage of successful checkouts out of all created checkouts.


# Credits Benefit
Source: https://docs.polar.sh/features/benefits/credits

Create your own Credits benefit

The Credits benefit allows you to credit a customer's Usage Meter balance.

<img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/benefits/credits/credits.light.png" />

<img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/benefits/credits/credits.dark.png" />

## Crediting Usage Meter Balance

The Credits benefit will credit a customer's Usage Meter balance at different points in time depending on the type of product purchased.

### Subscription Products

The customer will be credited the amount of units specified in the benefit at the beginning of every subscription cycle period — monthly or yearly.

### One-Time Products

The customer will be credited the amount of units specified in the benefit once at the time of purchase.

## Rollover unused credits

You can choose to rollover unused credits to the next billing cycle. This means that if a customer doesn't use all of their credits in a given billing cycle, the remaining credits will be added to their balance for the next billing cycle. To enable this feature, check the "Rollover unused credits" checkbox when creating or editing the Credits benefit.

<Note>
  If you change the rollover setting for a benefit, it will only apply to new
  credits issued after the change. Existing credits will not be affected.
</Note>


# Custom Benefit
Source: https://docs.polar.sh/features/benefits/custom

Create your own Custom benefit

You can add a simple, custom benefit, which allows you to attach a note to paying customers.

## **Custom Notes**

Secret message only customers can see, e.g [Cal.com](http://Cal.com) link, private email for support etc.

For custom integrations you can also distinguish benefits granted to customers to offer even more bespoke user benefits.


# Automate Discord Invites & Roles
Source: https://docs.polar.sh/features/benefits/discord-access

Sell Discord access & roles with ease

<img src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/benefits/discord/hero.png" />

Automating Discord server invites and roles for customers or subscribers is super easy and powerful with Polar.

* Fully automated Discord server invitations
* You can even setup multiple Discord servers, or...
* Offer different roles for different subscription tiers or products

## Create Discord Benefit

<img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/benefits/discord/create.light.png" />

<img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/benefits/discord/create.dark.png" />

Click on `Connect your Discord server`. You'll be redirected to Discord where you can grant the Polar App for your desired server.

Next, you'll be prompted to approve the permissions our app requires to function. It needs all of them.

### **Manage Roles**

Access to your Discord roles. You'll be able to select which ones to grant to your customers later.

### **Kick Members**

Ability to kick members who have this benefit and connected Discord with Polar.

### **Create Invite**

Ability to invite members who purchase a product or subscribes to a tier with this benefit.

You're now redirected back to Polar and can finish setting up the Discord benefit on our end.

### **Connected Discord server**

The Discord server you connected cannot be changed. However, you can create multiple benefits and connect more Discord servers if you want.

### **Granted role**

Which Discord role do you want to grant as part of this benefit?

## Adding Benefit to Product

Head over to the product you want to associate this new Discord benefit with. You should be able to toggle the benefit in the bottom of the Edit Product form.


# Automate Customer File Downloads
Source: https://docs.polar.sh/features/benefits/file-downloads

Offer digital file downloads with ease

<img src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/benefits/file-downloads/hero.png" />

## Sell Digital Products

You can easily offer customers and subscribers access to downloadable files with Polar.

* Up to 10GB per file
* Upload any type of file - from ebooks to full-fledged applications
* SHA-256 checksum validation throughout for you and your customers (if desired)
* Customers get a signed & personal downloadable URL

## Create Downloadable Benefit

1. Go to `Benefits` in the Dashboard sidebar
2. Click `+ Add Benefit` to create a new benefit
3. Choose `File Downloads` as the `Type`

You can now upload the files you want to offer as downloadables for customers.

1. Drag & drop files to the dropzone (`Feed me some bytes`)
2. Or click on that area to open a file browser

### Change filename

Click on the filename to change it inline.

### Change order of files

You can drag and drop the files in the order you want.

### Review SHA-256 checksum

Click on the contextual menu dots and then `Copy SHA-256 Checksum`

### Delete a file

Click on the contextual menu dots and then `Delete` in the menu.

**Active subscribers & customers will lose access too!**

Deleting a file permanently deletes it from Polar and our S3 buckets except for the metadata. Disable the file instead if you don't want it permanently deleted.

### Disable & Enable Files

You can disable files at any point to prevent new customers getting access to it.

**Existing customers retain their access**

Customers who purchased before the file was disabled will still have access to legacy files. Only new customers will be impacted.

**Enabling or adding files grants access retroactively**

In case you add more files or re-enable existing ones, all current customers and subscribers with the benefit will be granted access.


# Automate Private GitHub Repo(s) Access
Source: https://docs.polar.sh/features/benefits/github-access

Sell premium GitHub repository access with ease

<img src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/benefits/github/hero.png" />

## Sell GitHub Repository Access

With Polar you can seamlessly offer your customers and subscribers automated access to private GitHub repositories.

* Fully automated collaborator invites
* Unlimited repositories (via multiple benefits) from your organization(s)
* Users get access upon subscribing & removed on cancellation
* Or get lifetime access upon paying a one-time price (product)

### **Use cases**

* Sponsorware
* Access to private GitHub discussions & issues for sponsors
* Early access to new feature development before upstream push
* Premium educational materials & code
* Self-hosting products
* Courses, starter kits, open core software & more...

## Create GitHub Repository Benefit

1. Go to `Benefits` in the sidebar
2. Click `+ New Benefit` to create a new benefit
3. Choose `GitHub Repository Access` as the `Type`

You first need to `Connect your GitHub Account` and install a dedicated Polar App for this benefit across the repositories you want to use it with.

* Click `Connect your GitHub Account`

<Info>
  **Why do I need to connect GitHub again and install a separate app?**

  This feature requires permission to manage repository collaborators. GitHub Apps does not support progressive permission scope requests. So instead of requesting this sensitive permission from all users (unnecessarily) in our core GitHub Login this feature uses a standalone app instead.
</Info>

Once you've authorized our dedicated GitHub App for this feature you'll be redirected back to Polar and the benefit form - now connected and updated.

### **Repository**

Select the desired repository you want to automate collaborator invites for.

<Info>
  **Why can I only connect organization repositories vs. personal ones?**

  GitHub does not support granular permissions for collaborators on personal repositories - granting them all write permissions instead. Since collaborators would then be able to push changes, releases and more, we do not support personal repositories by default.Want this still? Reach out to us and we can enable it.
</Info>

### **Role**

Select the role you want to grant collaborators.

* **Read (Default & Highly recommended)**
* Triage
* Write
* Maintain
* Admin

Read access (read-only) is what 99.9% of cases should use and the others are highly discouraged unless you have special use cases & absolutely know the impact of these permissions. Checkout the [GitHub documentation](https://docs.github.com/en/organizations/managing-user-access-to-your-organizations-repositories/managing-repository-roles/repository-roles-for-an-organization#permissions-for-each-role) for reference.

<Info>
  Anyone with read access to a repository can create a pull request [(source)](https://docs.github.com/en/pull-requests/collaborating-with-pull-requests/proposing-changes-to-your-work-with-pull-requests/creating-a-pull-request).
</Info>

<Info>
  **Additional Costs for Paid GitHub Organizations**

  GitHub treats collaborators as a seat and they will incurr charges accordingly to your billing unless you're using a free GitHub organization plan. So make sure to confirm you're on a free plan OR charge sufficiently to offset the costs you'll need to pay to GitHub.
</Info>


# Automated Benefits
Source: https://docs.polar.sh/features/benefits/introduction



Polar offers built-in benefit (entitlements) automation for common upsells within the developer &
designer ecosystem with more to come.

* [**Credits**](/features/benefits/credits). A simple benefit that allows you to credit a customer's Usage Meter balance.
* [**License Keys**](/features/benefits/license-keys). Software license keys that you can customize the branding of.
* [**File Downloads**](/features/benefits/file-downloads). Downloadable files of any kind up to 10GB each.
* [**GitHub Repository Access**](/features/benefits/github-access). Automatically invite subscribers to private GitHub repo(s).
* [**Discord Invite**](/features/benefits/discord-access). Automate invitations and granting of roles to subscribers and customers.

## Product & Subscription Benefits

Product and subscription benefits are standalone resources in Polar - connected to one or many products or subscription tiers.

This approach is a bit different from other platforms, but offers many advantages:

* Easy to enable the same benefit across multiple products & subscriptions
* You can change a benefit in one place vs. many
* No duplicate data or work (error prone)
* More intuitive UI for you and your customers

**How customers get access to benefits:**

* ✅ Active subscribers of tiers with the benefit enabled
* ✅ Customers who bought a product with the benefit (lifetime access)
* ❌ Subscribers with an expired subscription (cancelled)
* ❌ Users who are not customers

## Creating & Managing Benefits

You can manage benefits in two ways:

1. Directly within a product create/edit form
2. Or via `Benefits` in your dashboard


# Automate Customer License Key Management
Source: https://docs.polar.sh/features/benefits/license-keys

Sell license key access to your service, software or APIs with ease

<img src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/benefits/license-keys/hero.jpeg" />

You can easily sell software license keys with Polar without having to deal with sales tax or hosting an API to validate them in real-time. License keys with Polar come with a lot of powerful features built-in.

* Brandable prefixes, e.g `POLAR_*****`
* Automatic expiration after `N` days, months or years
* Limited number of user activations, e.g devices
* Custom validation conditions
* Usage quotas per license key
* Automatic revokation upon cancelled subscriptions

## Create License Key Benefit

1. Go to `Benefits` in the sidebar
2. Click `+ New Benefit` to create a new benefit
3. Choose `License Keys` as the `Type`

### Custom Branding

Make your license keys standout with brandable prefixes, e.g `MYAPP_<AUTO_GENERATED_UUID4>`

### Automatic Expiration

Want license keys to expire automatically after a certain time period from when the customer bought them? No problem.

### Activation Limits

You can require license keys to be activated before future validation. A great feature in case you want to limit license key usage to a certain number of devices, IPs or other conditions.

**Enable user to deactivate instances via Polar.** Instead of building your own custom admin for customers to manage their activation instances - leave it to Polar instead.

### Usage Limit

Offering OpenAI tokens or anything else with a variable usage cost? You can set a custom usage quota per license key and increment usage upon validation.

## Customer Experience

Once customers buy your product or subscribes to your tier, they will automatically receive a unique license key. It's easily accessible to them under their purchases page.

Customers can:

* View & copy their license key
* See expiration date (if applicable)
* See usage left (if applicable)
* Deactivate activations (if enabled)

## Integrate API

It's super easy and straightforward to integrate Polar license keys into your application, library or API.

### Activate License Keys (Optional)

In case you've setup license keys to have a maximum amount of activation instances, e.g user devices. You'll then need to create an activation instance prior to validating license keys / activation.

**No activation limit?** You can skip this step.

```bash
curl -X POST https://api.polar.sh/v1/customer-portal/license-keys/activate
-H "Content-Type: application/json"
-d '{
  "key": "1C285B2D-6CE6-4BC7-B8BE-ADB6A7E304DA",
  "organization_id": "fda84e25-7b55-4d67-916d-60ead04ff61f",
  "label": "hello",
  "conditions": { "major_version": 1 },
  "meta": { "ip": "*************" }
}'
```

<ParamField path="key" type="string" required>
  Replace with the users license key (from input in your app).
</ParamField>

<ParamField path="organization_id" type="string" required>
  Replace with your organization ID here found in your settings.
</ParamField>

<ParamField path="label" type="string" required>
  Set a label to associate with this specific activation.
</ParamField>

<ParamField path="conditions" type="object">
  JSON object with custom conditions to validate against in the future, e.g IP, mac address, major version etc.
</ParamField>

<ParamField path="meta" type="object">
  JSON object with metadata to store for the users activation.
</ParamField>

#### **Response (200 OK)**

```json
{
  "id": "b6724bc8-7ad9-4ca0-b143-7c896fcbb6fe",
  "license_key_id": "508176f7-065a-4b5d-b524-4e9c8a11ed63",
  "label": "hello",
  "meta": {
    "ip": "*************"
  },
  "created_at": "2024-09-02T13:48:13.251621Z",
  "modified_at": null,
  "license_key": {
    "id": "508176f7-065a-4b5d-b524-4e9c8a11ed63",
    "organization_id": "fda84e25-7b55-4d67-916d-60ead04ff61f",
    "user_id": "d910050c-be66-4ca0-b4cc-34fde514f227",
    "benefit_id": "32a8eda4-56cf-4a94-8228-792d324a519e",
    "key": "1C285B2D-6CE6-4BC7-B8BE-ADB6A7E304DA",
    "display_key": "****-E304DA",
    "status": "granted",
    "limit_activations": 3,
    "usage": 0,
    "limit_usage": 100,
    "validations": 0,
    "last_validated_at": null,
    "expires_at": "2026-08-30T08:40:34.769148Z"
  }
}
```

### Validate License Keys

For each session of your premium app, library or API, we recommend you validate the users license key via the
[`/v1/customer-portal/license-keys/validate`](/api-reference/customer-portal/license-keys/validate) endpoint.

```bash
curl -X POST https://api.polar.sh/v1/customer-portal/license-keys/validate
-H "Content-Type: application/json"
-d '{
  "key": "1C285B2D-6CE6-4BC7-B8BE-ADB6A7E304DA",
  "organization_id": "fda84e25-7b55-4d67-916d-60ead04ff61f",
  "activation_id": "b6724bc8-7ad9-4ca0-b143-7c896fcbb6fe",
  "conditions": { "major_version": 1 },
  "increment_usage": 15
}'
```

<ParamField path="key" type="string" required>
  Replace with the users license key (from input in your app).
</ParamField>

<ParamField path="organization_id" type="string" required>
  Replace with your organization ID here found in your settings.
</ParamField>

<ParamField path="activation_id" type="string">
  The activation ID to validate - required in case activations limit is enabled and used (above).
</ParamField>

<ParamField path="conditions" type="object">
  In case of activation instances. Same exact JSON object as upon registration of the activation.
</ParamField>

<ParamField path="increment_usage" type="integer">
  In case you want to increment usage upon validation.
</ParamField>

#### **Response (200 OK)**

```json
{
  "id": "508176f7-065a-4b5d-b524-4e9c8a11ed63",
  "organization_id": "fda84e25-7b55-4d67-916d-60ead04ff61f",
  "user_id": "d910050c-be66-4ca0-b4cc-34fde514f227",
  "benefit_id": "32a8eda4-56cf-4a94-8228-792d324a519e",
  "key": "1C285B2D-6CE6-4BC7-B8BE-ADB6A7E304DA",
  "display_key": "****-E304DA",
  "status": "granted",
  "limit_activations": 3,
  "usage": 15,
  "limit_usage": 100,
  "validations": 5,
  "last_validated_at": "2024-09-02T13:57:00.977363Z",
  "expires_at": "2026-08-30T08:40:34.769148Z",
  "activation": {
    "id": "b6724bc8-7ad9-4ca0-b143-7c896fcbb6fe",
    "license_key_id": "508176f7-065a-4b5d-b524-4e9c8a11ed63",
    "label": "hello",
    "meta": {
      "ip": "*************"
    },
    "created_at": "2024-09-02T13:48:13.251621Z",
    "modified_at": null
  }
}
```

Validate `benefit_id` in case of multiple license keys

We require `organization_id` to be provided to avoid cases of Polar license keys being used across Polar organizations erroneously. Otherwise, a valid license key for one organization could be used on another.However, you are required to validate and scope license keys more narrowly within your organization if necessary. Offering more than one type of license key? Be sure to validate their unique benefit\_id in the responses.


# Embedded Checkout
Source: https://docs.polar.sh/features/checkout/embed

Embed our checkout directly on your site

<img src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/checkout/embed/demo.png" />

You can either copy and paste our code snippet to get up and running in a second or use our JavaScript library for more advanced integrations. Our embedded checkout allows you to provide a seamless purchasing experience without redirecting users away from your site.

## Code Snippet

The code snippet can be used on any website or CMS that allows you to insert HTML.

First, create a [Checkout Link](/features/checkout/links) as described in the previous section. The code snippet can directly be copied from there by clicking on `Copy Embed Code`.

The snippet looks like this:

```typescript
<a
  href="__CHECKOUT_LINK__"
  data-polar-checkout
  data-polar-checkout-theme="light"
>
  Purchase
</a>
<script
  src="https://cdn.jsdelivr.net/npm/@polar-sh/checkout@0.1/dist/embed.global.js"
  defer
  data-auto-init
></script>
```

This will display a `Purchase` link which will open an inline checkout when clicked.

You can style the trigger element any way you want, as long as you keep the `data-polar-checkout` attribute.

## Import Library

If you have a more advanced project in JavaScript, like a React app, adding the `<script>` tag may not be an option. In this case, you can install our dedicated library.

<CodeGroup>
  ```bash npm
  npm install @polar-sh/checkout
  ```

  ```bash pnpm
  pnpm add @polar-sh/checkout
  ```

  ```bash yarn
  yarn add @polar-sh/checkout
  ```
</CodeGroup>

Then, you should import the `PolarEmbedCheckout` helper class and manually call `PolarEmbedCheckout.init()`. This will add the required handlers on elements having the `data-polar-checkout` attribute.

Here is an example in React:

```ts
import { PolarEmbedCheckout } from '@polar-sh/checkout/embed'
import { useEffect } from 'react'

const PurchaseLink = () => {
  useEffect(() => {
    PolarEmbedCheckout.init()
  }, [])

  return (
    <a
      href="__CHECKOUT_LINK__"
      data-polar-checkout
      data-polar-checkout-theme="light"
    >
      Purchase
    </a>
  )
}

export default PurchaseLink
```

<Tip>
  Instead of a Checkout Link, you can also use a [Checkout Session](/features/checkout/session) URL created dynamically from the API.

  For this to work, make sure to set the [`embed_origin`](/api-reference/checkouts/create-session#body-embed-origin) parameter correctly when creating the Checkout Session. For example, if your checkout page is served on the URL `https://example.com/checkout`, you should set `embed_origin` to `https://example.com`.
</Tip>

## Advanced Integration

For users who need more control over the embedded checkout flow, the `PolarEmbedCheckout` class provides several advanced features.

### Programmatically creating an embed

Instead of using declarative triggers with `data-polar-checkout` attributes, you can programmatically create and control checkout instances:

```ts
import { PolarEmbedCheckout } from "@polar-sh/checkout/embed";

// Open checkout programmatically when needed
const openCheckout = async () => {
  const checkoutLink = "__CHECKOUT_LINK__";
  const theme = "light"; // or 'dark'

  try {
    // This creates the checkout iframe and returns a Promise
    // that resolves when the checkout is fully loaded
    const checkout = await PolarEmbedCheckout.create(checkoutLink, theme);

    // Now you can interact with the checkout instance
    return checkout;
  } catch (error) {
    console.error("Failed to open checkout", error);
  }
};

// Example: Trigger checkout when a button is clicked
document.getElementById("buy-button").addEventListener("click", () => {
  openCheckout();
});
```

### Listening for checkout events

You can listen for checkout events to respond to user interactions:

```ts
import { PolarEmbedCheckout } from "@polar-sh/checkout/embed";

const openCheckoutWithEvents = async () => {
  const checkout = await PolarEmbedCheckout.create("__CHECKOUT_LINK__");

  // Listen for when the checkout is loaded
  checkout.addEventListener("loaded", (event) => {
    console.log("Checkout loaded");
    // Call event.preventDefault() if you want to prevent the standard behavior
    // event.preventDefault()
    // Note: This would prevent removing the loader if it's still visible
  });

  // Listen for when the checkout has been closed
  checkout.addEventListener("close", (event) => {
    console.log("Checkout has been closed");
    // Call event.preventDefault() if you want to prevent the standard behavior
    // event.preventDefault()
  });

  // Listen for when the checkout has been confirmed (payment processing)
  checkout.addEventListener("confirmed", (event) => {
    console.log("Order confirmed, processing payment");
    // Call event.preventDefault() if you want to prevent the standard behavior
    // event.preventDefault()
    // Note: This would prevent setting the checkout as non-closable
  });

  // Listen for successful completion
  checkout.addEventListener("success", (event) => {
    console.log("Purchase successful!", event.detail);

    // Call event.preventDefault() if you want to prevent the standard behavior
    // event.preventDefault()
    // Note: For success event, this prevents automatic redirection if redirect is true

    // If redirect is false, you can show your own success message
    if (!event.detail.redirect) {
      showSuccessMessage();
    }
    // Otherwise, the user will be redirected to the success URL (unless prevented)
  });

  return checkout;
};
```

### React Integration with event handling

Here's a more complete React example that handles checkout events:

```ts
import { PolarEmbedCheckout } from '@polar-sh/checkout/embed'
import { useState, useEffect } from 'react'

const CheckoutButton = () => {
  const [checkoutInstance, setCheckoutInstance] = useState(null)

  // Clean up checkout instance on unmount
  useEffect(() => {
    return () => {
      if (checkoutInstance) {
        checkoutInstance.close()
      }
    }
  }, [checkoutInstance])

  const handleCheckout = async () => {
      try {
        const checkout = await PolarEmbedCheckout.create(
          '__CHECKOUT_LINK__',
          'light'
        )

      setCheckoutInstance(checkout)

      checkout.addEventListener('success', (event) => {
        // Track successful purchase
        analytics.track('Purchase Completed', {
          productId: 'your-product-id',
          // Add other analytics data
        })

        // Show success message or redirect
        if (!event.detail.redirect) {
          // Handle success in your app
        }
      })

      checkout.addEventListener('close', (event) => {
        // Clean up our reference when checkout is closed
        setCheckoutInstance(null)
      })
    } catch (error) {
      console.error('Failed to open checkout', error)
    }
  }

  return (
    <button onClick={handleCheckout}>
      Complete Purchase
    </button>
  )
}

export default CheckoutButton
```

### Programmatically closing checkout

In some cases, you might need to programmatically close the checkout - for instance, if you detect that a user needs to take an action elsewhere in your application first:

```ts
import { PolarEmbedCheckout } from "@polar-sh/checkout/embed";

// Example: open checkout and store the instance
let activeCheckout = null;

async function openCheckout() {
  const checkout = await PolarEmbedCheckout.create("__CHECKOUT_LINK__");
  activeCheckout = checkout;
  return checkout;
}

// Later, close it programmatically when needed
function closeCheckout() {
  if (activeCheckout) {
    activeCheckout.close();
    // The 'close' event will fire automatically
    // Don't set activeCheckout to null here, as we'll handle that in the event listener
  }
}

// Add a listener to update our reference when checkout is closed
function setupCheckout(checkout) {
  checkout.addEventListener("close", (event) => {
    // Reset our reference when checkout is closed
    activeCheckout = null;
  });
  return checkout;
}

// Example usage
document.getElementById("open-checkout").addEventListener("click", async () => {
  const checkout = await openCheckout();
  setupCheckout(checkout);
});
document
  .getElementById("close-checkout")
  .addEventListener("click", closeCheckout);
```

## Enabling Wallet Payment Methods (Apple Pay, Google Pay, etc.)

Wallet payment methods such as Apple Pay and Google Pay are supported in the checkout with the following conditions:

* **Apple Pay** appears automatically in the checkout if:
  * The user is on an Apple device
  * The browser is Safari
  * The device is connected to an Apple account with Apple Pay configured

* **Google Pay** appears automatically in the checkout if:
  * The user is on Google Chrome
  * The browser is connected to a Google account with Google Pay configured

**No additional action is required** if you meet these conditions and are not using an embedded checkout.

### Enabling Wallet Payments for Embedded Checkout

By default, wallet payment methods (Apple Pay, Google Pay, etc.) are **not enabled** when you embed our checkout form into your website. For security reasons, your website domain needs to be manually validated before enabling these payment methods in embedded mode.

To enable wallet payment methods on your embedded checkout, please [email us](mailto:<EMAIL>) with:

* Your organization slug
* The domain you wish to allow for wallet payments


# Checkout Links
Source: https://docs.polar.sh/features/checkout/links

Sell your digital products with ease by sharing a checkout link to select products

Checkout links can be shared or linked on your website which automatically
creates a checkout session for customers.

<Tip>
  Looking for a way to generate Checkout session programmatically? Checkout
  Links might not be the right tool for you. Instead, you should use the
  [Checkout API](/features/checkout/session).
</Tip>

## Create a Checkout Link

Checkout Links can be managed from the **Checkout Links** tabs of the Products section. Click on **New Link** to create a new one.

<Frame>
  <img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/checkout/links/create.light.png" />

  <img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/checkout/links/create.dark.png" />
</Frame>

#### Label

This is an internal name for the Checkout Link. It's only visible to you.

#### Products

You can select one or **several** products. With several products, customers will be able to switch between them on the checkout page.

<img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/checkout/links/checkout_multiple_products.light.png" />

<img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/checkout/links/checkout_multiple_products.dark.png" />

#### Discount

You can disable discount codes, if you wish to prevent customers from using them.

You can also preset a discount: it'll be automatically applied when the customer lands on the checkout page.

#### Metadata

This is an optional key-value object allowing you to store additional information which may be useful for you when handling the order. This metadata will be copied to the generated Checkout object and, if the checkout succeeds, to the resulting Order and/or Subscription.

## Using Checkout Links

You can share the Checkout Link URL on your webpage, social media, or directly to customers.

<img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/checkout/links/checkout_link.light.png" />

<img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/checkout/links/checkout_link.dark.png" />

<Warning>
  Checkout Links will go against our API, and redirect to short-lived Checkout session. This means that the Checkout page the user will end up on, are temporary and expires after a while if no successful purchase is made.

  This means that you need to make sure to always use this Checkout Link URL (as shown above). If you mistakenly copy the URL from a Checkout Session, the link will expire.
</Warning>

### Query parameters

You can pass optional query parameters to your Checkout Links.

#### Prepopulate fields

You can prefill the checkout fields with the following query parameters:

<ParamField path="customer_email" type="string">
  Prefill customer email at checkout
</ParamField>

<ParamField path="customer_name" type="string">
  Prefill customer name at checkout
</ParamField>

<ParamField path="discount_code" type="string">
  Prefill discount code
</ParamField>

<ParamField path="amount" type="string">
  Prefill amount in case of Pay What You Want pricing
</ParamField>

<ParamField path="custom_field_data.{slug}" type="string">
  Prefill checkout fields data, where `{slug}` is the slug of the custom field.
</ParamField>

#### Store attribution and reference metadata

The following query parameters will automatically be set on Checkout [`metadata`](/api-reference/checkouts/get-session#response-metadata).

<ParamField path="reference_id" type="string">
  Your own reference ID for the checkout session.
</ParamField>

<ParamField path="utm_source" type="string">
  UTM source of the checkout session.
</ParamField>

<ParamField path="utm_medium" type="string">
  UTM medium of the checkout session.
</ParamField>

<ParamField path="utm_campaign" type="string">
  UTM campaign of the checkout session.
</ParamField>

<ParamField path="utm_content" type="string">
  UTM content of the checkout session.
</ParamField>

<ParamField path="utm_term" type="string">
  UTM term of the checkout session.
</ParamField>


# Checkout API
Source: https://docs.polar.sh/features/checkout/session

Create checkout sessions programmatically for complete control

If you want to integrate more deeply the checkout process with your website or application, you can use our dedicated API.

The first step is to [create a Checkout session](/api-reference/checkouts/create-session). For this you'll need at least your **Product ID**.

You can retrieve your Product ID from Products in your dashboard, click on "context-menu" button in front of your product and click on Copy Product ID.

The API will return you an object containing all the information about the session, including **an URL where you should redirect your customer** so they can complete their order.

## Multiple products

You can create a checkout session with multiple products. This is useful if you want to allow your customers to choose between different products before they checkout.

<img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/checkout/session/checkout_multiple_products.light.png" />

<img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/checkout/session/checkout_multiple_products.dark.png" />

## External Customer ID

Quite often, you'll have your own users management system in your application, where your customer already have an ID. To ease reconciliation between Polar and your system, you can inform us about your customer ID when creating a checkout session through the [`external_customer_id`](/api-reference/checkouts/create-session/) field.

After a successful checkout, we'll create a Customer on Polar with the external ID you provided. It'll be provided through the `customer.external_id` property in webhooks you may have configured.

## SDK examples

Using our SDK, creating a checkout session is quite straightforward.

<CodeGroup>
  ```ts TypeScript
  import { Polar } from "@polar-sh/sdk";

  const polar = new Polar({
    accessToken: process.env["POLAR_ACCESS_TOKEN"] ?? "",
  });

  async function run() {
    const checkout = await polar.checkouts.create({
      products: ["productId"]
    });

    console.log(checkout.url)
  }

  run();
  ```

  ```py Python
  from polar_sdk import Polar

  with Polar(
      access_token="<YOUR_BEARER_TOKEN_HERE>",
  ) as polar:

      checkout = polar.checkouts.create(request={
          "allow_discount_codes": True,
          "product_id": "<value>",
      })

      print(checkout.url)
  ```
</CodeGroup>


# Custom Fields
Source: https://docs.polar.sh/features/custom-fields

Learn how to add custom input fields to your checkout with Polar

By default, the Checkout form will only ask basic information from the customer to fulfill the order: a name, an email address, billing information, etc. But you might need more! A few examples:

* A checkbox asking the customer to accept your terms
* An opt-in newsletter consent
* A select menu to ask where they heard from you
* ...

With Polar, you can easily add such fields to your checkout using **Custom Fields**.

## Create Custom Fields

Custom Fields are managed at an organization's level. To create them, go to **Settings** and **Custom Fields**. You'll see the list of all the available fields on your organization.

<img className="block dark:hidden" src="https://mintcdn.com/polar/assets/features/custom-fields/custom_fields.light.png?maxW=3840&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=654aeea80a69a6e5e2de3848aa6a45bd" width="3840" height="2400" data-path="assets/features/custom-fields/custom_fields.light.png" srcset="https://mintcdn.com/polar/assets/features/custom-fields/custom_fields.light.png?w=280&maxW=3840&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=f882385630687304a7fddf11fca00ec8 280w, https://mintcdn.com/polar/assets/features/custom-fields/custom_fields.light.png?w=560&maxW=3840&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=15305ce132ae8e0ef02a33336bcd7557 560w, https://mintcdn.com/polar/assets/features/custom-fields/custom_fields.light.png?w=840&maxW=3840&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=24f75621f47cb1b1cea4c50238a1bce5 840w, https://mintcdn.com/polar/assets/features/custom-fields/custom_fields.light.png?w=1100&maxW=3840&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=030ec5877e3ff375ded3871e775778a6 1100w, https://mintcdn.com/polar/assets/features/custom-fields/custom_fields.light.png?w=1650&maxW=3840&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=e3413411c9f256930977c975d88d2b01 1650w, https://mintcdn.com/polar/assets/features/custom-fields/custom_fields.light.png?w=2500&maxW=3840&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=9bc9bbdf1b2fa299546e9a134db0836b 2500w" data-optimize="true" data-opv="2" />

<img className="hidden dark:block" src="https://mintcdn.com/polar/assets/features/custom-fields/custom_fields.dark.png?maxW=3840&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=de9aa68938e33f46b63ba0816aaf4909" width="3840" height="2400" data-path="assets/features/custom-fields/custom_fields.dark.png" srcset="https://mintcdn.com/polar/assets/features/custom-fields/custom_fields.dark.png?w=280&maxW=3840&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=92f107d123cab6b6e5560d4d0f2399d8 280w, https://mintcdn.com/polar/assets/features/custom-fields/custom_fields.dark.png?w=560&maxW=3840&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=a9ffa15e7a87ad222cd8d9bfb8956802 560w, https://mintcdn.com/polar/assets/features/custom-fields/custom_fields.dark.png?w=840&maxW=3840&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=05a47d4a38a8350391dead0879e6cc22 840w, https://mintcdn.com/polar/assets/features/custom-fields/custom_fields.dark.png?w=1100&maxW=3840&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=6e157f57fd892de3c55a0ad0fa6f5284 1100w, https://mintcdn.com/polar/assets/features/custom-fields/custom_fields.dark.png?w=1650&maxW=3840&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=867f5238f71e69266c3b75f090bb6943 1650w, https://mintcdn.com/polar/assets/features/custom-fields/custom_fields.dark.png?w=2500&maxW=3840&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=1e58eef978352d38cebdacb53a629710 2500w" data-optimize="true" data-opv="2" />

Click on **New Custom Field** to create a new one.

### Type

The type of the field is the most important thing to select. It determines what type of input will be displayed to the customer during checkout.

The type can't be changed after the field is created.

We support five types of fields:

#### Text

This will display a simple text field to input textual data. By default, it'll render a simple input field but you can render a **textarea** by toggling the option under `Form input options`.

Under `Validation constraints`, you can add minimum and maximum length validation.

Underneath, the data will be stored as a string.

#### Number

This will display a number input field. Under `Validation constraints`, you can add minimum and maximum validation.

Underneath, the data will be stored as a number.

#### Date

This will display a date input field. Under `Validation constraints`, you can add minimum and maximum validation.

Underneath, the data will be stored as a string using the ISO 8601 format.

#### Checkbox

This will display a checkbox field.

Underneath, the data will be stored as a boolean (`true` or `false`).

#### Select

This will display a select field with a predefined set of options. Each option is a pair of `Value` and `Label`, the first one being the value that'll be stored underneath and the latter the one that will be shown to the customer.

### Slug and name

The slug determines the key that'll be used to store the data inside objects related to the checkout, like Orders and Subscriptions. It must be unique across your organization. You can change it afterwards, we'll automatically update the data to reflect the new slug.

The name is what we'll be displayed to you to recognize the field across your dashboard. By default, it'll also be the label of the field displayed to the customer, unless you customize it under `Form input options`.

<img className="block dark:hidden" src="https://mintcdn.com/polar/assets/features/custom-fields/create_custom_field.light.png?maxW=1620&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=6c272e382da43d751c81834728677f55" width="1620" height="2334" data-path="assets/features/custom-fields/create_custom_field.light.png" srcset="https://mintcdn.com/polar/assets/features/custom-fields/create_custom_field.light.png?w=280&maxW=1620&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=d53c091938960407c7bb911b0d4bf281 280w, https://mintcdn.com/polar/assets/features/custom-fields/create_custom_field.light.png?w=560&maxW=1620&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=aa6582defa8105d5327f1fd332a7cf0c 560w, https://mintcdn.com/polar/assets/features/custom-fields/create_custom_field.light.png?w=840&maxW=1620&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=f1bc311d93f952998abf8c6c4d9988a8 840w, https://mintcdn.com/polar/assets/features/custom-fields/create_custom_field.light.png?w=1100&maxW=1620&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=4c4a3a10833d1377eaff634fc7ec70cc 1100w, https://mintcdn.com/polar/assets/features/custom-fields/create_custom_field.light.png?w=1650&maxW=1620&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=5b2a09650357ce63a4aa0bb72465ca87 1650w, https://mintcdn.com/polar/assets/features/custom-fields/create_custom_field.light.png?w=2500&maxW=1620&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=45a6ed37738ff2d48317904242c1bf95 2500w" data-optimize="true" data-opv="2" />

<img className="hidden dark:block" src="https://mintcdn.com/polar/assets/features/custom-fields/create_custom_field.dark.png?maxW=1620&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=89fbc983a057f7bae926a8d409e06aac" width="1620" height="2334" data-path="assets/features/custom-fields/create_custom_field.dark.png" srcset="https://mintcdn.com/polar/assets/features/custom-fields/create_custom_field.dark.png?w=280&maxW=1620&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=565029be1a04546366c16ba1bda8c512 280w, https://mintcdn.com/polar/assets/features/custom-fields/create_custom_field.dark.png?w=560&maxW=1620&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=5a168e15614e88fbfc506733f129b79a 560w, https://mintcdn.com/polar/assets/features/custom-fields/create_custom_field.dark.png?w=840&maxW=1620&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=1e9474397367d5320be72a046f98eba9 840w, https://mintcdn.com/polar/assets/features/custom-fields/create_custom_field.dark.png?w=1100&maxW=1620&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=09b3c36637b94080002bd09e196c40b9 1100w, https://mintcdn.com/polar/assets/features/custom-fields/create_custom_field.dark.png?w=1650&maxW=1620&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=3709ed57cea13a1ff4b03a2c01e16201 1650w, https://mintcdn.com/polar/assets/features/custom-fields/create_custom_field.dark.png?w=2500&maxW=1620&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=cd2b1ccfde440ef5582a8d97259ca709 2500w" data-optimize="true" data-opv="2" />

### Form input options

Those options allow you to customize how the field is displayed to the customer. You can set:

* The label, displayed above the field
* The help text, displayed below the field
* The placeholder, displayed inside the field when there is no value

The label and help text supports basic Markdown syntax, so you can add bold, italic or even links.

<img className="block dark:hidden" src="https://mintcdn.com/polar/assets/features/custom-fields/label_markdown.light.png?maxW=1383&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=48bc4113de7363db72a5334ae41e432f" width="1383" height="132" data-path="assets/features/custom-fields/label_markdown.light.png" srcset="https://mintcdn.com/polar/assets/features/custom-fields/label_markdown.light.png?w=280&maxW=1383&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=a3615472d21d13bd4352c6c5092c03e5 280w, https://mintcdn.com/polar/assets/features/custom-fields/label_markdown.light.png?w=560&maxW=1383&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=cede8962c664ceab35878548d9afb5b5 560w, https://mintcdn.com/polar/assets/features/custom-fields/label_markdown.light.png?w=840&maxW=1383&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=a7c01fce4ba432b5095323b25b3f99f0 840w, https://mintcdn.com/polar/assets/features/custom-fields/label_markdown.light.png?w=1100&maxW=1383&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=a031f0dde12b9ceb85fedb3dec64e88c 1100w, https://mintcdn.com/polar/assets/features/custom-fields/label_markdown.light.png?w=1650&maxW=1383&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=6b6ecf4b4a7ff692d49dd9d1d714b213 1650w, https://mintcdn.com/polar/assets/features/custom-fields/label_markdown.light.png?w=2500&maxW=1383&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=4a48972c5bf2484d38031d38b0deeae2 2500w" data-optimize="true" data-opv="2" />

<img className="hidden dark:block" src="https://mintcdn.com/polar/assets/features/custom-fields/label_markdown.dark.png?maxW=1383&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=d61bc360e9a4bbba72d41b40776c1344" width="1383" height="132" data-path="assets/features/custom-fields/label_markdown.dark.png" srcset="https://mintcdn.com/polar/assets/features/custom-fields/label_markdown.dark.png?w=280&maxW=1383&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=6fad2c758e408c18a3b8113b484d4680 280w, https://mintcdn.com/polar/assets/features/custom-fields/label_markdown.dark.png?w=560&maxW=1383&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=079e60756a93bd250321cf7a7497e94c 560w, https://mintcdn.com/polar/assets/features/custom-fields/label_markdown.dark.png?w=840&maxW=1383&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=3f13d338730441e2564cc95ad014e36d 840w, https://mintcdn.com/polar/assets/features/custom-fields/label_markdown.dark.png?w=1100&maxW=1383&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=d7b4f6c3519f7870ac86d83b9e821ef8 1100w, https://mintcdn.com/polar/assets/features/custom-fields/label_markdown.dark.png?w=1650&maxW=1383&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=8b475356d773ef2412929bc9b5ece6ba 1650w, https://mintcdn.com/polar/assets/features/custom-fields/label_markdown.dark.png?w=2500&maxW=1383&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=e5571f0f268bc44b3fdcad657483816e 2500w" data-optimize="true" data-opv="2" />

## Add Custom Field to Checkout

Custom Fields are enabled on Checkout specifically on each **product**. While [creating or updating](/features/products) a product, you can select the custom fields you want to include in the checkout for this product.

<img className="block dark:hidden" src="https://mintcdn.com/polar/assets/features/custom-fields/add_custom_field.light.png?maxW=3837&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=f8dec0e79f394859aebeb41767c5225f" width="3837" height="2400" data-path="assets/features/custom-fields/add_custom_field.light.png" srcset="https://mintcdn.com/polar/assets/features/custom-fields/add_custom_field.light.png?w=280&maxW=3837&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=c62bfce9334bae5dacdc660433c524ab 280w, https://mintcdn.com/polar/assets/features/custom-fields/add_custom_field.light.png?w=560&maxW=3837&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=daa8b5e5295b792deabc04e5728b56f9 560w, https://mintcdn.com/polar/assets/features/custom-fields/add_custom_field.light.png?w=840&maxW=3837&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=77dbe65145ea86215df5b9a39f210de1 840w, https://mintcdn.com/polar/assets/features/custom-fields/add_custom_field.light.png?w=1100&maxW=3837&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=0f431fe940a6331d162f6c0003a4b6b4 1100w, https://mintcdn.com/polar/assets/features/custom-fields/add_custom_field.light.png?w=1650&maxW=3837&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=bc3c3f51a6048acbe66693fb554f6419 1650w, https://mintcdn.com/polar/assets/features/custom-fields/add_custom_field.light.png?w=2500&maxW=3837&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=603b41827815e45c413f4551c9def9bd 2500w" data-optimize="true" data-opv="2" />

<img className="hidden dark:block" src="https://mintcdn.com/polar/assets/features/custom-fields/add_custom_field.dark.png?maxW=3837&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=ca4f2c9c298a14e3b14f14b52666c505" width="3837" height="2400" data-path="assets/features/custom-fields/add_custom_field.dark.png" srcset="https://mintcdn.com/polar/assets/features/custom-fields/add_custom_field.dark.png?w=280&maxW=3837&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=db7d1041e72e80929992f095fd6aaffc 280w, https://mintcdn.com/polar/assets/features/custom-fields/add_custom_field.dark.png?w=560&maxW=3837&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=d8765c072bfaafb91ba63d214907fe7a 560w, https://mintcdn.com/polar/assets/features/custom-fields/add_custom_field.dark.png?w=840&maxW=3837&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=8a3af41fcb4ab34f49529bea7d8ec405 840w, https://mintcdn.com/polar/assets/features/custom-fields/add_custom_field.dark.png?w=1100&maxW=3837&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=dad9568daf1c3950dc6c528db798c9ea 1100w, https://mintcdn.com/polar/assets/features/custom-fields/add_custom_field.dark.png?w=1650&maxW=3837&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=5f383efe16bc4214e4856ddc6c06ca57 1650w, https://mintcdn.com/polar/assets/features/custom-fields/add_custom_field.dark.png?w=2500&maxW=3837&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=fdc42046324db2dc48b909f0aea6c3be 2500w" data-optimize="true" data-opv="2" />

Note that you can make the field `Required`.

<Tip>
  If you make a **checkbox** field **required**, customers will have to check
  the box before submitting the checkout. Very useful for terms acceptance!
</Tip>

The fields are now added as part of the Checkout form for this product.

<img className="block dark:hidden" src="https://mintcdn.com/polar/assets/features/custom-fields/custom_fields_checkout.light.png?maxW=1866&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=f1bbac1b394e31822ab87410aa9b8cd1" width="1866" height="4245" data-path="assets/features/custom-fields/custom_fields_checkout.light.png" srcset="https://mintcdn.com/polar/assets/features/custom-fields/custom_fields_checkout.light.png?w=280&maxW=1866&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=b93a17b5299679e0b17f5b62090d0f1b 280w, https://mintcdn.com/polar/assets/features/custom-fields/custom_fields_checkout.light.png?w=560&maxW=1866&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=c0f2e1a49f766a3ca76bfde69e98f07f 560w, https://mintcdn.com/polar/assets/features/custom-fields/custom_fields_checkout.light.png?w=840&maxW=1866&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=87e74d5f7d3a3b8e5f2e82480bcaa483 840w, https://mintcdn.com/polar/assets/features/custom-fields/custom_fields_checkout.light.png?w=1100&maxW=1866&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=040696c27f27ca02313eca098ff0d6cd 1100w, https://mintcdn.com/polar/assets/features/custom-fields/custom_fields_checkout.light.png?w=1650&maxW=1866&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=d54140699063a64f7615db33b19d8419 1650w, https://mintcdn.com/polar/assets/features/custom-fields/custom_fields_checkout.light.png?w=2500&maxW=1866&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=527189f4e3dd0b8272ed43b8cba8918f 2500w" data-optimize="true" data-opv="2" />

<img className="hidden dark:block" src="https://mintcdn.com/polar/assets/features/custom-fields/custom_fields_checkout.dark.png?maxW=1866&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=f2c31b8741a34a80f887906d5cdc04e5" width="1866" height="4245" data-path="assets/features/custom-fields/custom_fields_checkout.dark.png" srcset="https://mintcdn.com/polar/assets/features/custom-fields/custom_fields_checkout.dark.png?w=280&maxW=1866&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=ded91c62b7a81a5ff603d38751026bb6 280w, https://mintcdn.com/polar/assets/features/custom-fields/custom_fields_checkout.dark.png?w=560&maxW=1866&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=77d17d0ccdb5c7a2f37816fe3180fb3d 560w, https://mintcdn.com/polar/assets/features/custom-fields/custom_fields_checkout.dark.png?w=840&maxW=1866&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=46d485cb85afae9ef8092440611a1850 840w, https://mintcdn.com/polar/assets/features/custom-fields/custom_fields_checkout.dark.png?w=1100&maxW=1866&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=5c18626bc275bf7dc41de9d51c2342d0 1100w, https://mintcdn.com/polar/assets/features/custom-fields/custom_fields_checkout.dark.png?w=1650&maxW=1866&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=d682992dce7041a11001df1b87c2e7ea 1650w, https://mintcdn.com/polar/assets/features/custom-fields/custom_fields_checkout.dark.png?w=2500&maxW=1866&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=f2af44213a0aefbb2ff8c7b4a87b2d3c 2500w" data-optimize="true" data-opv="2" />

## Read data

Once you have added Custom Fields to your organization, they'll be automatically displayed as a column in your `Sales` page, both on Orders and Subscriptions. From there, you'll be able to see the data input by the customer.

<img className="block dark:hidden" src="https://mintcdn.com/polar/assets/features/custom-fields/custom_field_data.light.png?maxW=3840&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=c72210717703e314543a586809639389" width="3840" height="2400" data-path="assets/features/custom-fields/custom_field_data.light.png" srcset="https://mintcdn.com/polar/assets/features/custom-fields/custom_field_data.light.png?w=280&maxW=3840&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=218d73400fec99a7e966d61c2614bfc8 280w, https://mintcdn.com/polar/assets/features/custom-fields/custom_field_data.light.png?w=560&maxW=3840&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=e3309b3ec86d560110d81ae6a66515be 560w, https://mintcdn.com/polar/assets/features/custom-fields/custom_field_data.light.png?w=840&maxW=3840&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=990078ab3d9a16f14ab3d26c6b2bc172 840w, https://mintcdn.com/polar/assets/features/custom-fields/custom_field_data.light.png?w=1100&maxW=3840&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=cee9eaeadd396feca465bc6161b96e5c 1100w, https://mintcdn.com/polar/assets/features/custom-fields/custom_field_data.light.png?w=1650&maxW=3840&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=83fb7760b3b87c9121ee66e35bc6595d 1650w, https://mintcdn.com/polar/assets/features/custom-fields/custom_field_data.light.png?w=2500&maxW=3840&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=f42986f9cccb87fd362faf77bc12d5a9 2500w" data-optimize="true" data-opv="2" />

<img className="hidden dark:block" src="https://mintcdn.com/polar/assets/features/custom-fields/custom_field_data.dark.png?maxW=3840&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=b0051f5b02fcb10d5875cb1a3370cfae" width="3840" height="2400" data-path="assets/features/custom-fields/custom_field_data.dark.png" srcset="https://mintcdn.com/polar/assets/features/custom-fields/custom_field_data.dark.png?w=280&maxW=3840&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=0f88909be63817db714123fe2869abeb 280w, https://mintcdn.com/polar/assets/features/custom-fields/custom_field_data.dark.png?w=560&maxW=3840&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=9294bb6f2c73320d698b8de6726e0c25 560w, https://mintcdn.com/polar/assets/features/custom-fields/custom_field_data.dark.png?w=840&maxW=3840&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=b1443ad02e17f89be13ac114ecbb52d1 840w, https://mintcdn.com/polar/assets/features/custom-fields/custom_field_data.dark.png?w=1100&maxW=3840&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=881932e6071cfa39040eeaab439a520e 1100w, https://mintcdn.com/polar/assets/features/custom-fields/custom_field_data.dark.png?w=1650&maxW=3840&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=572a7afe72dfb1d623a5ad896af15112 1650w, https://mintcdn.com/polar/assets/features/custom-fields/custom_field_data.dark.png?w=2500&maxW=3840&auto=format&n=ZfK336ao4nPWv8dy&q=85&s=d846c31eff8b8e9d542d0382acc796d4 2500w" data-optimize="true" data-opv="2" />

This data is also available from the [Orders](/api-reference/orders/get) and [Subscriptions](/api-reference/subscriptions/get) API, under the `custom_field_data` property. Each value is referenced by the **slug** of the field.

```json
{
  // ...
  "custom_field_value": {
    "terms": true,
    "source": "social_media"
  }
}
```


# Customer Management
Source: https://docs.polar.sh/features/customer-management

Get insights on your customers and sales

<img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/customer-management/details.light.png" />

<img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/customer-management/details.dark.png" />

## Managing Customers

Polar has a built in feature to view and manage your Customers.

Everyone who has ever purchased something from you will be recorded as a Customer to your Organization. You’re able to see past orders and their ongoing subscriptions, as well as some additional metrics.

## External ID

Quite often, you'll have our own users management system in your application, where your customer already have an ID. To ease reconciliation between Polar and your system, we have a dedicated [`external_id`](/api-reference/customers/get-external#response-external-id) field on Customers. It's unique across your organization and can't be changed once set.

We have dedicated API endpoints that work with the `external_id` field, so you don't even have to store the internal Polar ID in your system.

<Card title="Get Customer by External ID" icon="link" href="/api-reference/customers/get-external" horizontal />

<Card title="Update Customer by External ID" icon="link" href="/api-reference/customers/update-external" horizontal />

<Card title="Delete Customer by External ID" icon="link" href="/api-reference/customers/delete-external" horizontal />

## Metadata

You may set additional metadata on Customers. This can be very useful to store additional data about your customer you want to be available through our API and webhooks.

<img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/customer-management/edit.light.png" />

<img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/customer-management/edit.dark.png" />

It can be set through the dashboard or through the [API](/api-reference/customers/update#body-metadata). It can also be pre-set when creating a Checkout Session by using the [`customer_metadata`](/api-reference/checkouts/create-session#body-customer-metadata) field. This way, after a successful checkout, the metadata will automatically be set on the newly created Customer.


# Customer Portal
Source: https://docs.polar.sh/features/customer-portal

Enable customers to view & manage orders and subscriptions easily

The Customer Portal is a destination where your customers can see their orders and ongoing subscriptions. It’s also where they’re able to get hands on receipts, benefits, and more.

<img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/customer-portal/overview.light.png" />

<img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/customer-portal/overview.dark.png" />

## Redirect to your Customer Portal

The customer portal is directly available from the URL `https://polar.sh/your-org-slug/portal`. Your customers will be able to authenticate there by entering the email they used to purchase or subscribe to your products.

<img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/customer-portal/signin.light.png" />

<img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/customer-portal/signin.dark.png" />

Customer Portal Sign In

## Creating an authenticated Customer Portal Link

You can provide a pre-authenticated Customer Portal Link to your customers. This is handy if you want to redirect them directly from your application.

Using the Polar API, all you need is to call the `customerSessions` endpoint. Here’s an example using our TypeScript SDK.

```typescript
import { Polar } from "@polar-sh/sdk";

const polar = new Polar({
  accessToken: process.env["POLAR_ACCESS_TOKEN"] ?? "",
});

async function run() {
  const result = await polar.customerSessions.create({
    customerId: "<value>",
  });

  redirect(result.customerPortalUrl)
}

run();
```

Or, if you use NextJS as your framework, we have a handy utility which shortens down your code significantly.

```typescript
// app/portal/route.ts
import { CustomerPortal } from "@polar-sh/nextjs";

export const GET = CustomerPortal({
  accessToken: process.env.POLAR_ACCESS_TOKEN,
  getCustomerId: async (req) => '<value>',
  server: 'sandbox' // Use sandbox if you're testing Polar - pass 'production' otherwise
});
```


# Discounts
Source: https://docs.polar.sh/features/discounts

Create discounts on products and subscriptions

Discounts are a way to reduce the price of a product or subscription. They can be applied to one-time purchasable products or subscriptions.

<img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/discounts/create.light.png" />

<img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/discounts/create.dark.png" />

## Create a discount

Go to the **Products** page and click on the **Discounts** tab.

#### Name

Displayed to the customer when they apply the discount.

#### Code

Optional code (case insensitive) that the customer can use to apply the discount. If left empty, the discount can only be applied through a Checkout Link or the API.

#### Percentage Discount

The percentage discount to apply to the product or subscription.

#### Fixed Amount Discount

The amount discount to apply to the product or subscription.

#### Recurring Discount

The percentage discount to apply to the product or subscription.

* **Once** The discount is applied once.
* **Several Months** The discount is applied for a fixed number of months.
* **Forever** The discount is applied indefinitely.

#### Restrictions

* **Products** The discount can only be applied to specific products.
* **Starts at** The discount can only be applied to customers in specific countries.
* **Ends at** The discount can only be applied to customers in specific countries.
* **Maximum redemptions** The maximum number of times the discount can be applied.

## Apply a discount

Discounts can be applied to a Checkout Link or a Checkout Session.


# Setup a Payout Account
Source: https://docs.polar.sh/features/finance/accounts



## Connect Payout Account

You need to setup an account so that we can issue [payouts](/features/finance/payouts).

<img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/finance/accounts/onboarding.light.jpeg" />

<img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/finance/accounts/onboarding.dark.jpeg" />

1. Go to the `Finance` page in your Polar dashboard
2. Click `Setup` in the card shown above in your dashboard
3. Choose account type & follow their setup instructions

*This is only required the first time and you can do this proactively too in order - recommended to avoid any additional delays.*

### Stripe Connect Express

<img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/finance/accounts/create.light.jpeg" />

<img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/finance/accounts/create.dark.jpeg" />

Stripe is the default and recommended option since it enables instant transfers.


# Account Balance & Transparent Fees
Source: https://docs.polar.sh/features/finance/balance

Monitor your Polar balance without hidden fees

You can see your available balance for payout at any time under your `Finance` page.

## Polar Balance

<img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/finance/balance/overview.light.jpeg" />

<img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/finance/balance/overview.dark.jpeg" />

Your balance is all the earnings minus:

1. Any VAT we've captured for remittance, i.e balance is excluding VAT
2. Our revenue share (4% + 40¢)

All historic transactions are available in chronological order along with their associated fees that have been deducted.

Note: Upon [payout (withdrawal)](/features/finance/payouts), Stripe incurs additional fees that will be deducted before the final payout of the balance.


# Payouts
Source: https://docs.polar.sh/features/finance/payouts

Easily withdraw money from your Polar account at any time

You can issue a withdrawal, i.e payout, at any time once there is at least **\$10 on your balance**. We will then transfer the balance minus Stripe payout fees (see below) to your Stripe account & issue a payout on their side.

## Manual Withdrawal

We require this to be done manually since:

1. Users have requested control for easier accounting vs. frequent & small payouts
2. Giving users control of Stripe payout fees

## **Stripe Payout Fees**

1. \$2 per month of active payout(s)
2. 0.25% + \$0.25 per payout
3. Cross border fees (currency conversion): 0.25% (EU) - 1% in other countries.

Given the fixed costs, we want to default to manual payouts so you can control when you want to incur them and do it once vs. per each individual transaction in order to reduce the overall fees.

## Reverse invoices

Since we're the Merchant of Record, your customers get an invoice from Polar. Thus, for your accounting, you need to issue an invoice to Polar for the amount we paid out to you. To ease this process, we can automatically generate a **reverse invoice** for you, detailing the sells we made on your behalf, minus our fees.

You can generate them from the **Payouts** page under **Finance** in your Polar dashboard. Click on the ellipsis next to the payout you want to generate a reverse invoice for, and select **Download invoice**.

<img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/finance/payouts/download.light.png" />

<img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/finance/payouts/download.dark.png" />

A modal will open, allowing you to:

* Set your billing name and address.
* Add information shown below your billing address.
* Add notes shown at the bottom of the invoice.
* Customize the invoice number. By default, we generate one like `POLAR-0001`, but you can change it to your own format and sequence.

<img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/finance/payouts/generate.light.png" />

<img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/finance/payouts/generate.dark.png" />

<Warning>
  Once the reverse invoice is generated, it cannot be changed. Make sure to
  double-check the information before generating it.
</Warning>

### Sample Reverse Invoice

<iframe src="https://polar-public-assets.s3.us-east-2.amazonaws.com/sample-reverse-invoice.pdf" width="100%" height="600px" />

## Frequently Asked Questions

<AccordionGroup>
  <Accordion title="How long do payouts take?">
    Payouts typically take 4-7 business days.
  </Accordion>

  <Accordion title="Can I use my personal bank account to receive payouts?">
    For individual accounts, yes. For business accounts, you will need a business bank account.
  </Accordion>
</AccordionGroup>


# Affonso Affiliates with Polar
Source: https://docs.polar.sh/features/integrations/affonso



This guide explains how to integrate
[Affonso](https://affonso.io)'s affiliate marketing software with your Polar
account to track and manage affiliate-driven sales for your SaaS business.

## What is Affonso?

[Affonso](https://affonso.io) is an affiliate marketing software that enables SaaS businesses to launch, manage, and scale their own affiliate programs. With Affonso, you can:

* Set up flexible commission structures
* Manage multiple affiliate programs from one dashboard
* Provide your affiliates with real-time tracking and marketing resources
* Automate affiliate payments and commission calculations

## Integration Steps

### 1. Create a Polar Access Token for Affonso

First, you'll need to create an API token in Polar that Affonso can use to communicate with your account:

1. Login to your **Polar Dashboard**
2. Navigate to **Settings** in the main menu
3. Scroll down to the **Developers** section on the Settings page
4. Click the **New token** button
5. Give your token a name (e.g., "Affonso Integration")
6. Set token expiration to **No expiration**
   Important: If you set an expiration date, you'll need to manually update the token in Affonso when it expires. Tracking will stop working if the token expires.
7. Enable all the following scopes:
   * discounts:read
   * discounts:write
   * events:read
   * subscriptions:read
   * customers:read
   * customers:write
   * orders:read
   * refunds:read
   * webhooks:read
   * webhooks:write
8. Click **Create token** and copy the generated token
9. Provide this token to Affonso by entering it [in their integration settings](https://affonso.io/app/affiliate-program/connect)

<video controls width="600">
  <source src="https://affonso-videos.s3.eu-central-1.amazonaws.com/Connect_Polar_Affonso_1.mp4" type="video/mp4" />
</video>

### 2. Set Up Webhooks in Polar

After connecting your Polar account with Affonso, you'll [receive a webhook URL and secret from Affonso](https://affonso.io/app/affiliate-program/connect). Add these to your Polar account:

1. Go to **Settings** → **Developers** → **Webhooks** in your Polar Dashboard
2. Click the **"Add Endpoint"** button
3. In the URL field, paste the webhook URL provided by Affonso
4. For Format, select **RAW** from the dropdown
5. In the Secret field, paste the webhook secret provided by Affonso
6. Under Events, enable all of the following:
   * `order.created`
   * `order.refunded`
   * `subscription.canceled`
7. Click **Save** to complete the webhook setup

<video controls width="600">
  <source src="https://affonso-videos.s3.eu-central-1.amazonaws.com/Connect_Polar_Affonso_2.mp4" type="video/mp4" />
</video>

### 3. Add the Affonso Tracking Script to Your Website

Add Affonso's tracking script to the `<head>` tag of your website:

```html
<!-- Place in <head> tag -->
<script
  async
  defer
  src="https://affonso.io/js/pixel.min.js"
  data-affonso="YOUR_AFFONSO_PROGRAM_ID"
  data-cookie_duration="YOUR_COOKIE_DURATION">
</script>
```

Replace `YOUR_AFFONSO_PROGRAM_ID` with the unique program ID provided by Affonso.

This script should be placed on all pages of your website, including:

* Your main marketing website
* Your application domain
* Any subdomains where users might land or make purchases

### 4. Track User Signups (Optional)

For better conversion insights, you can track when users sign up through an affiliate link:

```javascript
// After successful registration
window.Affonso.signup(userEmail);
```

### 5. Pass Referral Data to Polar Checkout

To ensure proper commission attribution, pass the referral data when creating checkout sessions:

```javascript
// Get the referral ID from the Affonso global variable
const referralId = window.affonso_referral;

// Create checkout session with Polar
const checkout = await polar.checkouts.create({
  products: ["your_product_id"],
  success_url: "https://your-site.com/success",
  metadata: {
    affonso_referral: referralId, // Include referral ID from Affonso
  }
});

// Redirect to checkout
window.location.href = checkout.url;
```

## How It Works

1. When a user visits your site through an affiliate link, Affonso's script stores a unique identifier in a cookie
2. If you've implemented signup tracking, Affonso records when the user creates an account
3. When the user makes a purchase, the referral ID is passed to Polar as metadata
4. Polar's webhook notifies Affonso about the purchase
5. Affonso attributes the sale to the correct affiliate and calculates the commission

## Benefits of the Integration

* **Automated Tracking**: No manual work required to track affiliate-driven sales
* **Real-Time Analytics**: Both you and your affiliates get immediate insights into performance
* **Seamless User Experience**: The integration works behind the scenes without affecting your checkout flow
* **Flexible Commission Structures**: Set up complex commission rules based on product, subscription duration, etc.

## Getting Help

More details about the integration: [Polar Affiliate Program](https://affonso.io/polar-affiliate-program)

If you need assistance with your Affonso integration, contact Affonso's support team:

* Email: [<EMAIL>](mailto:<EMAIL>)
* Live chat: Available directly in the Affonso dashboard


# Polar Integration in Fernand
Source: https://docs.polar.sh/features/integrations/fernand

Learn how to sync customer and payment data from Polar to Fernand.

<img src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/integrations/fernand/overview.png" />

## What is Fernand?

[Fernand](https://getfernand.com/) is a modern customer support tool designed for SaaS — it’s fast, calm, and built to reduce the anxiety of answering support requests.

## How it works

After connecting your [Polar](https://polar.sh/) account to Fernand, you’ll be able to see customer payment information and product access details directly within each customer conversation.

This enables you to:

* Instantly verify if someone is an active customer
* Prioritize conversations from high-tier plans
* View product purchases and payment history in context

***

## How to connect Fernand with Polar

<img src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/integrations/fernand/enable.png" />

1. Open [Integrations](https://app.getfernand.com/settings/organization/integrations) in your Fernand organization settings.
2. Click on **Connect Polar**.
3. You'll be redirected to Polar to authorize the connection.
4. Once approved, Fernand will begin syncing customer data automatically.

That’s it! You’ll now see Polar customer info directly in Fernand's conversation list and sidebar.

***

## How to automate your inbox with Polar data

Once Polar is connected, you can create automation rules in Fernand based on Polar data.

Let’s walk through a basic example: auto-replying to all customers on your `Pro` plan.

<img src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/integrations/fernand/inbox.png" />

### Create a new rule

<Steps>
  <Step title="Create a new rule">
    1. Go to [Rules](https://app.getfernand.com/settings/organization/rules) in Fernand.
    2. Click `Add rule` and give it a descriptive name.

    <img src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/integrations/fernand/create-rule.png" />
  </Step>

  <Step title="Select a trigger">
    This ensures the rule runs on each new customer message.

    <img src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/integrations/fernand/rule-trigger.png" />
  </Step>

  <Step title="Select a condition">
    Now add a condition based on Polar data. For example:

    * `Contact is a customer...`
    * `Contact has paid plan...`

    You can target specific plans (e.g. `Pro`, `Business`) or specific products to personalize support or automate prioritization.

    <img src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/integrations/fernand/rule-conditions.png" />
  </Step>

  <Step title="Select an action">
    Now define what happens when the rule matches. For example:

    * Send an auto reply (with variables)
    * Assign the conversation to a specific agent
    * Tag the conversation with `priority` or `paid`
    * Trigger a webhook for external automation

    <img src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/integrations/fernand/action.png" />
  </Step>
</Steps>

### Disconnecting the integration

If you ever want to disconnect Polar from your Fernand workspace:

<Steps>
  <Step title="Go to the Integrations page." />

  <Step title="Click Disconnect next to Polar." />
</Steps>

Deleting your organization on Fernand will also remove the Polar integration automatically.


# Polar for Framer
Source: https://docs.polar.sh/features/integrations/framer

The fastest way to sell digital products on your Framer site

Introducing the official Polar plugin for Framer. Allowing you to sell products on your site without having to build a custom checkout flow.

![](https://www.framer.com/marketplace/_next/image/?url=https%3A%2F%2Fy4pdgnepgswqffpt.public.blob.vercel-storage.com%2Fplugins%2F174-egCWZYwZbpLc42xnGQIY42F1KqtNDk\&w=1920\&q=100)

## Getting Started

[Get your hands on the Polar plugin in the Framer Marketplace](https://www.framer.com/marketplace/plugins/polar/)


# Purchase Power Parity with ParityDeals
Source: https://docs.polar.sh/features/integrations/paritydeals

Offer products with different price across the globe

Want to offer different prices in different countries? [ParityDeals](https://www.paritydeals.com/) offers [automatic pricing optimizations depending on customers geolocation](https://www.paritydeals.com/features/purchasing-power-parity-discounts/) and a seamless integration with Polar.

## Simple Integration, Powerful Deals

* You can easily and securely (OAuth 2.0) connect Polar to ParityDeals
* Select products on Polar to offer deals for
* Configure deals by country or holidays
* ParityDeals automatically creates and manages discounts on Polar
* Showing them to customers based on time and geolocation (unless VPN is detected)
* Offering great & local deals internationally with ease

## Setup Guide

### Signup to ParityDeals

Go to [app.paritydeals.com](http://app.paritydeals.com) and sign up.

### Connect Polar on ParityDeals

In your ParityDeals dashboard, click `Create Deals` > `Create Deals with Polar`.

<img src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/integrations/paritydeals/connect.light.png" />

### Grant ParityDeals Access (OAuth 2.0)

No need to create API access keys and share them externally. Just connect securely and grant the necessary permissions using Polar OAuth 2.0.

<img src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/integrations/paritydeals/grant.light.png" />

### Choose Products

Now, let's select the Polar products you want to offer deals for.

<img src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/integrations/paritydeals/choose-products.light.png" />

### Configure Deals

Let's configure our deal settings.

* Enter your website URL (requires your own site vs. Polar storefront)
* Enter a targeted URL path, e.g `/pricing` to only show deals on that page

<img src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/integrations/paritydeals/create-deals.light.png" />

Now we can configure the deals for different countries. ParityDeals offers great defaults, but you can of course change them.

<img src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/integrations/paritydeals/customize-deals.light.png" />

### Configure Banner

You can then customize the ParityDeals banner to suit your site and design.

<img src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/integrations/paritydeals/customize-banner.light.png" />

### Embed Banner

Finally, we're all setup over at ParityDeals. Just copy the script to their banner and embed it on your site. You're now done 👏🏼

<img src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/integrations/paritydeals/success.light.png" />

## Questions & Help

Checkout the [ParityDeals documentation](https://www.paritydeals.com/docs/) for more guides and information.


# Polar for Raycast
Source: https://docs.polar.sh/features/integrations/raycast

The fastest way to access Polar from your keyboard

<img src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/integrations/raycast/hero.png" />

## Install Extension

[Head over to Polar on the Raycast Store, and install it from there.](https://www.raycast.com/emilwidlund/polar)

### View Orders

Easily view orders across organizations.

![](https://files.raycast.com/acvj8yffxqxbnv82lhtsnf7u7x29)

### View Subscriptions

View all active subscriptions across your organizations.

![](https://files.raycast.com/y6he77j6ig6hchxbpxdcsd2i1yjf)

### View Customers

Keep track of all your customers.


# Polar for Zapier
Source: https://docs.polar.sh/features/integrations/zapier

Connect Polar to hundreds of other apps with Zapier

export const ZapierEmbed = () => {
  if (typeof document === "undefined") {
    return null;
  } else {
    setTimeout(() => {
      const script = document.createElement("script");
      script.type = "module";
      script.src = "https://cdn.zapier.com/packages/partner-sdk/v0/zapier-elements/zapier-elements.esm.js";
      document.head.appendChild(script);
      const stylesheet = document.createElement("link");
      stylesheet.rel = "stylesheet";
      stylesheet.href = "https://cdn.zapier.com/packages/partner-sdk/v0/zapier-elements/zapier-elements.css";
      document.head.appendChild(stylesheet);
      const element = document.createElement("zapier-workflow");
      element.clientId = "Zci4gpfx7Co47mBoFOYm0m8bmnzB5UPcw7eGhpSR";
      element.theme = document.querySelector("html").classList.contains("dark") ? "dark" : "light";
      element.introCopyDisplay = "hide";
      element.manageZapsDisplay = "hide";
      element.guessZapDisplay = "hide";
      const container = document.querySelector("#zapier-container") || document.body;
      container.appendChild(element);
    }, 1);
    return <div id="zapier-container"></div>;
  }
};

[Zapier](https://zapier.com/apps/polar/integrations) lets you connect Polar to 2,000+ other web services. Automated connections called Zaps, set up in minutes with no coding, can automate your day-to-day tasks and build workflows between apps that otherwise wouldn't be possible.

Each Zap has one app as the **Trigger**, where your information comes from and which causes one or more **Actions** in other apps, where your data gets sent automatically.

<Note>
  We've focused on **triggers** (webhooks) for now, so you can react to events in Polar and trigger actions in other apps.

  Need to perform actions in Polar? Tell us about your use case [here](https://github.com/orgs/polarsource/discussions/new?category=integrations\&labels=integrations%2Fzapier) and we'll consider adding more actions in the future.
</Note>

## Getting Started with Zapier

Sign up for a free [Zapier](https://zapier.com/apps/polar/integrations) account, from there you can jump right in. To help you hit the ground running, you'll find popular pre-made Zaps below.

## How do I connect Polar to Zapier?

Log in to your [Zapier account](https://zapier.com/sign-up) or create a new account.
Navigate to "My Apps" from the top menu bar.
Now click on "Connect a new account..." and search for "Polar"
Use your credentials to connect your Polar account to Zapier.
Once that's done you can start creating an automation! Use a pre-made Zap or create your own with the Zap Editor. Creating a Zap requires no coding knowledge and you'll be walked step-by-step through the setup.
Need inspiration? See everything that's possible with [Polar and Zapier](https://zapier.com/apps/Polar/integrations).

If you have any additional questions, you can open a ticket with Zapier Support from [https://zapier.com/app/get-help](https://zapier.com/app/get-help)

## Popular use cases

<ZapierEmbed />


# Orders & Subscriptions
Source: https://docs.polar.sh/features/orders



## Sales

<img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/orders/overview.light.png" />

<img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/orders/overview.dark.png" />

The sales view shows you all sales in a paginated list.

## Order & Subscription Details

<img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/orders/detail.light.png" />

<img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/orders/detail.dark.png" />

Each sale has metadata attached to it. Common properties like

* Amount
* Tax Amount
* Invoices
* Customer
  * Basic Customer Details
  * Past Orders

## Checkouts

<img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/orders/checkouts.light.png" />

<img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/orders/checkouts.dark.png" />

You can also have an overview of all checkout sessions. You can filter them by customer email, status and product.

A checkout can be in the following states:

* Open: The checkout session is open and waiting for the customer to complete the payment.
* Confirmed: The customer clicked the **Pay** or **Subscribe** button and the payment is being processed.
* Succeeded: The payment was successful and the order was created.
* Expired: The checkout session expired and the customer can no longer complete it. A new checkout session must be created.

If you click on a Checkout, you can have more details on the **payment attempts**, in particular, why a payment has failed or has been declined.

<img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/orders/checkout.light.png" />

<img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/orders/checkout.dark.png" />


# Products
Source: https://docs.polar.sh/features/products

Create digital products on Polar in minutes

<Note>
  **Everything is a product**

  Subscriptions or pay once products are both considered a product in Polar (API & data model). Just with different pricing & billing logic. So both are shown & managed under Products with the ability to filter based on pricing model.
</Note>

<img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/products/create.light.png" />

<img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/products/create.dark.png" />

## Create a product

### Name & Description

Starting off with the basic.

* **Name** The title of your product.
* **Description** Markdown is supported here too.

### Pricing

Determine how you want to charge your customers for this product.

<Steps>
  <Step title="Billing cycle">
    * **One-time purchase** Customer is charged once and gets access to the product forever.
    * **Monthly** Customer is charged every month.
    * **Yearly** Customer is charged every year.
  </Step>

  <Step title="Pricing type">
    * **Fixed price** Set a fixed price for the product.
    * **Pay what you want** Let customers decide how much they want to pay.
    * **Free** No charge for the product.
  </Step>

  <Step title="Price">
    For fixed price products, set the amount you want to charge.

    For pay what you want products, you can set a minimum amount and a default amount that will be preset on checkout.
  </Step>
</Steps>

<Warning>
  Billing cycle and pricing type cannot be changed after the product is created.
</Warning>

<Note>
  **What if I want both a monthly and yearly pricing?**

  Polar has a unique approach to what the industry typically calls **variants**. Each product has a single pricing model, but you can create multiple products with different pricing models, and showcase them both at checkout.
</Note>

### Product Media

* You can upload public product images to be displayed on product pages
* They can be up to 10MB each
* You can remove and re-arrange images

### Checkout Fields

You can collect additional information from your customers at checkout. This can be useful for things like phone number, terms of service agreement or specific data you need to collect.

<img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/products/checkout_fields.light.png" />

<img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/products/checkout_fields.dark.png" />

Fields are managed from your organization settings, and you can choose which fields to show on a per-product basis, and set if they are required or not. We support the following field types:

* Text
* Number
* Date
* Checkbox
* Select

<Tip>
  If you make a checkbox **required**, the customer will need to check it before
  confirming their purchase. Very handy for legal terms!
</Tip>

The data collected will be available in the order and subscription details.

### Automated Entitlements

Finally, you can enable or create new entitlements (what we call Benefits) that you tie to the product.

Read more in our [product benefits guide](/features/benefits/introduction) on how they work and how to customize the built-in ones we offer:

* License Keys
* Discord Server Role
* GitHub Repository Access
* File Downloads
* Custom Benefit

## Variants

Polar has a unique approach regarding what the industry typically calls **variants**.

We believe having a single product with multiple pricing models and benefits adds unnecessary complexity to the user and to the API. Instead, we chose to treat everything as a product, giving you maximum flexibility about the pricing and benefits you want to offer.

You can showcase several products at checkout, allowing the customer to switch between them. Typically, you can offer a monthly and a yearly product, with specific pricing and benefits for each. Read more about how to do so using [Checkout Links](/features/checkout/links) or the [Checkout Session API](/features/checkout/session).

## Update a product

You can edit any product details, except the **billing cycle** and **pricing type**.

For fixed price products, you can change the price. Existing subscribers will remain on their current pricing.

If you add benefits, existing subscribers will get them automatically. If you remove benefits, existing subscribers will lose access to them.

## Archive a product

Products on Polar can't be deleted, but they can be **archived**. You can do so by clicking the **Archive** button on the bottom right of the product page.

Existing customers will keep their access to the product, and subscriptions will continue to renew. However, the product will no longer be available for new purchases.

It's possible to unarchive a product using the [Products Update API](/api-reference/products/update#body-is-archived).

## Subscription Trials

Polar does not have a built-in trial period. However, you can achieve similar functionality by associating a discount with the subscription product.


# Manage Refunds
Source: https://docs.polar.sh/features/refunds

You can easily refund orders on Polar - both in full or in parts.

No matter what refund policy you offer to customers, Polar makes it easy to offer both full and partial refunds to easily deliver the customer experience and refund policy you want.

However, even in case you have a “no refund” policy, Polar reserves the right to issue refunds within 60 days of purchase - at our own discretion. We reserve this right in an effort to automatically and proactively reduce costly chargebacks.

<img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/refunds/order-refund.light.png" />

<img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/refunds/order-refund.dark.png" />

<Note>
  **Polar can issue refunds on your behalf**

  Polar reserves the right to issue refunds within 60 days of purchase, at its own discretion, in order to prevent chargebacks. So if you choose to have a “no refunds” policy, be aware that Polar could still issue refunds in an effort to proactively prevent chargebacks.
</Note>

## Issuing a refund

1. Go to the order details page for the specific order you want to refund
2. Scroll down to the “Refund” section
3. Click “Refund”

<img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/refunds/issue-refund.light.png" />

<img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/refunds/issue-refund.dark.png" />

**Amount**

Specify the amount to refund. By default it’s the full order amount, but you can reduce this to issue a partial refund instead.

<Warning>
  **Payment fees are not refunded**

  Unfortunately, credit card networks and PSPs charge us for the underlying transactions regardless of whether it’s later refunded (industry standard). Therefore, we cannot offer a refund on our fees since the costs remain constant.

  Example: An order of $30 costs ~$1.6 in fees to Polar. You can still refund the customer $30, but the ~$1.6 fee remains and is deducted on your balance from other purchases.
</Warning>

**Reason**

Select the reason for the refund - helpful for future reference.

**Revoke Benefits (One-time purchases)**

For one-time purchases, you can revoke the customers access to product benefits, e.g file downloads, license keys or Discord/GitHub invites. By default this is selected since we default to a full refund, but can be disabled.

**Revoke Benefits (Subscriptions)**

You cannot revoke access by refunding an order associated with a subscription. Instead the subscription is required to be canceled and Polar will then automatically revoke access once the subscription itself is revoked.


# Billing
Source: https://docs.polar.sh/features/usage-based-billing/billing

How billing works with Usage Based

## Metered Pricing

Metered Pricing is a pricing model where you charge your customers based on the usage of your application.

There are a few different pricing models unique to Usage Based Billing:

* Unit Pricing
* Volume Pricing *(coming soon)*

### Unit Pricing

Unit pricing is a simple pricing model where you charge a fixed amount for each unit of usage.

For example:

| Product Meter       | Price per unit |
| ------------------- | -------------- |
| `prompt-tokens`     | \$0.10         |
| `completion-tokens` | \$0.18         |

This means that every unit of `prompt-tokens` consumed by a customer will be charged at \$0.10 and every unit of `completion-tokens` will be charged at \$0.18.

It's a linear pricing model, where the price per unit is fixed.

### Volume Pricing *(coming soon)*

Volume pricing is a pricing model where you charge a fixed amount for a certain volume of usage. Volume pricing is not yet available, but will be coming soon.

## Invoicing Customers for Usage

Our Usage Based Billing infrastructure is built to work with Subscription products out of the box.

### Add a metered price to your product

To charge your customers for usage, you need to add a metered price to your product. You'll need the select the **Meter** and the **amount per unit**.

<img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/usage/cap.light.png" />

<img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/usage/cap.dark.png" />

Optionally, you can set a **cap**. The customer will be charged the cap amount if they exceed it, regardless of the usage.

### Monthly Invoicing

If a customer has a subscription with a monthly billing period, usage is aggregated monthly and invoiced at the end of the month with the rest of the subscription.

### Yearly Invoicing

If a customer has a subscription with a yearly billing period, usage is aggregated yearly and invoiced at the end of the year with the rest of the subscription.

### Usage Charges and Subscription Cancellation

When a subscription is canceled, it generally remains active until the end of the current billing period (known as the grace period). During this grace period, all accumulated usage-based charges continue to be tracked. A final invoice will be issued at the end of that period to cover the consumed usage, even if the subscription will not be renewed. This ensures no pending usage charges are lost.

<Warning>
  If a [discount](/features/discounts) is applied on the subscription, it'll be
  applied on the **whole invoice**, including metered usage.
</Warning>

## Customer Portal

Customers can view their estimated charges for each meter in the Customer Portal.

<img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/usage/portal.light.png" />

<img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/usage/portal.dark.png" />


# Credits
Source: https://docs.polar.sh/features/usage-based-billing/credits

Crediting customers for Usage Based Billing

Credits is the way to pre-pay for usage in Polar. It allows you to give your customers the ability to pre-pay for usage instead of risk getting a hefty bill at the end of the month.

## How Credits Work

When you ingest events into a Usage Meter, customers will be charged for the usage based on the product's pricing model.

However, sometimes you may want to give your customers the ability to pre-pay for usage instead of risk getting a hefty bill at the end of the month.

When you issue Credits to a customer, we first deduct the Credits from their Usage Meter balance. If the Usage Meter balance reaches 0, the customer will be charged for the overage.

### Credits-only spending

To avoid any overage charges, don't create any Metered price on your product. This way, billing won't be triggered at all for the meter

## Issuing Credits with the Credits Benefit

<img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/benefits/credits/credits.light.png" />

<img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/benefits/credits/credits.dark.png" />

The Credits benefit will credit a customer's Usage Meter balance at different points in time depending on the type of product the benefit is attached to.

### Subscription Products

The customer will be credited the amount of units specified in the benefit at the beginning of every subscription cycle period — monthly or yearly.

### One-Time Products

The customer will be credited the amount of units specified in the benefit once at the time of purchase.

## Tracking customer's balance

In your application, you'll likely need to track the customer's balance for a given meter. The easiest way to do this is to use the [Customer State](/integrate/customer-state), which will give you the overview of the customer, including the balance for each of their active meters.

You can also specifically query the meters balance using the [Customer Meters API](/api-reference/customer-meters/list).

<Warning>
  Polar doesn't block usage if the customer exceeds their balance. You're
  responsible for implementing the logic you need to prevent usage if they
  exceed it.
</Warning>


# Event Ingestion
Source: https://docs.polar.sh/features/usage-based-billing/event-ingestion

Ingest events from your application

Events are the core of Usage Based Billing. They represent *some* usage done by a customer in your application. Typical examples of events are:

* A customer consumed AI LLM tokens
* A customer streamed minutes of video
* A customer uploaded a file to your application

Events are sent to Polar using the [Events Ingestion API](/api-reference/events/ingest) and are stored in our database. An event consists of the following fields:

* A `name`, which is a string that can be used to identify the type event. For example, `ai_usage`, `video_streamed` or `file_uploaded`.
* A `customer_id` or `external_customer_id`, which is the Polar's customer ID or your user's ID. This is used to identify the customer that triggered the event.
* A `metadata` object, which is a JSON object that can contain any additional information about the event. This is useful for storing information that can be used to filter the events or compute the actual usage. For example, you can store the duration of the video streamed or the size of the file uploaded.

Here is an example of an event:

```json
{
  "name": "ai_usage",
  "external_customer_id": "cus_123",
  "metadata": {
    "model": "gpt-4.1-nano",
    "requests": 1,
    "total_tokens": 77,
    "request_tokens": 58,
    "response_tokens": 19
  }
}
```

## Ingest events using the Polar SDK

To ingest events, you can use the Polar SDKs.

### TypeScript Example

```typescript
import { Polar } from "@polar-sh/sdk";

const polar = new Polar({
  accessToken: process.env["POLAR_ACCESS_TOKEN"] ?? "",
});

await polar.events.ingest({
  events: [
    {
      name: "<value>",
      externalCustomerId: "<id>",
      metadata: {
        key: "value",
      },
    },
  ],
});
```

<Note>
  You are always responsible for checking the balance of your customers' Usage
  Meter. As events always are ingested, we will never prohibit any customer's
  action based on their Usage Meter balance.
</Note>

## Ingestion Strategies

To make it easier to ingest events, we have created a set of ingestion strategies for common event sources.

Learn more about our [Ingestion Strategies](/features/usage-based-billing/ingestion-strategies).

## Good to know

### Events are immutable

Once an event is ingested, it cannot be changed, nor can it be deleted.


# Delta Time Strategy
Source: https://docs.polar.sh/features/usage-based-billing/ingestion-strategies/delta-time-strategy

Ingest delta time of arbitrary execution

## Javascript SDK

Ingest delta time of arbitrary execution. Bring your own now-resolver.

```
pnpm add @polar-sh/ingestion
```

```typescript
import { Ingestion } from "@polar-sh/ingestion";
import { DeltaTimeStrategy } from "@polar-sh/ingestion/strategies/DeltaTime";

const nowResolver = () => performance.now();
// const nowResolver = () => Number(hrtime.bigint())
// const nowResolver = () => Date.now()

// Setup the Delta Time Ingestion Strategy
const deltaTimeIngestion = Ingestion({
  accessToken: process.env.POLAR_ACCESS_TOKEN,
})
  .strategy(new DeltaTimeStrategy(nowResolver))
  .ingest("execution-time");

export async function GET(request: Request) {
  try {
    // Get the wrapped start clock function
    // Pass Customer Id to properly annotate the ingestion events with a specific customer
    const start = deltaTimeIngestion.client({
      customerId: request.headers.get("X-Polar-Customer-Id") ?? "",
    });

    const stop = start();

    await sleep(1000);

    // { deltaTime: xxx } is automatically ingested to Polar
    const delta = stop();

    return Response.json({ delta });
  } catch (error) {
    return Response.json({ error: error.message });
  }
}
```

#### Ingestion Payload

```json
{
  "customerId": "123",
  "name": "execution-time",
  "metadata": {
    "deltaTime": 1000
  }
}
```


# Strategy Introduction
Source: https://docs.polar.sh/features/usage-based-billing/ingestion-strategies/ingestion-strategy

Ingestion strategies for Usage Based Billing

Polar offers an ingestion framework to work with Polar's event ingestion API.

Want to report events regarding Large Language Model usage, S3 file uploads or something else? Our Ingestion strategies are customized to make it as seamless as possible to fire ingestion events for complex needs.

* [LLM Strategy](/features/usage-based-billing/ingestion-strategies/llm-strategy)
* [S3 Strategy](/features/usage-based-billing/ingestion-strategies/s3-strategy)
* [Stream Strategy](/features/usage-based-billing/ingestion-strategies/stream-strategy)
* [Delta Time Strategy](/features/usage-based-billing/ingestion-strategies/delta-time-strategy)

### Help us improve

We're always looking for ways to improve our ingestion strategies. Feel free to contribute — [Polar Ingestion SDK](https://github.com/polarsource/polar-ingestion).


# LLM Strategy
Source: https://docs.polar.sh/features/usage-based-billing/ingestion-strategies/llm-strategy

Ingestion strategy for LLM Usage

## Javascript SDK

### LLM Strategy

Wrap any LLM model from the `@ai-sdk/*` library, to automatically fire prompt- & completion tokens used by every model call.

```
pnpm add @polar-sh/ingestion ai @ai-sdk/openai
```

```typescript
import { Ingestion } from "@polar-sh/ingestion";
import { LLMStrategy } from "@polar-sh/ingestion/strategies/LLM";
import { generateText } from "ai";
import { openai } from "@ai-sdk/openai";

// Setup the LLM Ingestion Strategy
const llmIngestion = Ingestion({ accessToken: process.env.POLAR_ACCESS_TOKEN })
  .strategy(new LLMStrategy(openai("gpt-4o")))
  .ingest("openai-usage");

export async function POST(req: Request) {
  const { prompt }: { prompt: string } = await req.json();

  // Get the wrapped LLM model with ingestion capabilities
  // Pass Customer Id to properly annotate the ingestion events with a specific customer
  const model = llmIngestion.client({
    customerId: request.headers.get("X-Polar-Customer-Id") ?? "",
  });

  const { text } = await generateText({
    model,
    system: "You are a helpful assistant.",
    prompt,
  });

  return Response.json({ text });
}
```

#### Ingestion Payload

```json
{
  "customerId": "123",
  "name": "openai-usage",
  "metadata": {
    "promptTokens": 100,
    "completionTokens": 200
  }
}
```

## Python SDK

Our Python SDK includes an ingestion helper and strategies for common use cases. It's installed as part of the Polar SDK.

<CodeGroup>
  ```bash pip
  pip install polar-sdk
  ```

  ```bash uv
  uv add polar-sdk
  ```
</CodeGroup>

### Ingestion helper

The ingestion helper is a simple wrapper around the Polar events ingestion API. It takes care of batching and sending events to Polar in the background, without blocking your main thread.

```python
import os
from polar_sdk.ingestion import Ingestion

ingestion = Ingestion(os.getenv("POLAR_ACCESS_TOKEN"))

ingestion.ingest({
    "name": "my-event",
    "external_customer_id": "CUSTOMER_ID",
    "metadata": {
        "usage": 13.37,
    }
})
```

### PydanticAI Strategy

[PydanticAI](https://ai.pydantic.dev) is an AI agent framework for Python. A common use-case with AI applications is to track the usage of LLMs, like the number of input and output tokens, and bill the customer accordingly.

With our PydanticAI strategy, you can easily track the usage of LLMs and send the data to Polar for billing.

```python
import os
from polar_sdk.ingestion import Ingestion
from polar_sdk.ingestion.strategies import PydanticAIStrategy
from pydantic import BaseModel
from pydantic_ai import Agent


ingestion = Ingestion(os.getenv("POLAR_ACCESS_TOKEN"))
strategy = ingestion.strategy(PydanticAIStrategy, "ai_usage")


class MyModel(BaseModel):
    city: str
    country: str


agent = Agent("gpt-4.1-nano", output_type=MyModel)

if __name__ == '__main__':
    result = agent.run_sync("The windy city in the US of A.")
    print(result.output)
    strategy.ingest("CUSTOMER_ID", result)
```

*This example is inspired from the [Pydantic Model example](https://ai.pydantic.dev/examples/pydantic-model/) of PydanticAI documentation.*

#### Ingestion Payload

```json
{
  "name": "ai_usage",
  "external_customer_id": "CUSTOMER_ID",
  "metadata": {
    "requests": 1,
    "total_tokens": 78,
    "request_tokens": 58,
    "response_tokens": 20
  }
}
```


# S3 Strategy
Source: https://docs.polar.sh/features/usage-based-billing/ingestion-strategies/s3-strategy

Ingestion strategy for S3 Operations

## Javascript SDK

Wrap the official AWS S3 Client with our S3 Ingestion Strategy to automatically ingest bytes uploaded.

```
pnpm add @polar-sh/ingestion @aws-sdk/client-s3
```

```typescript
import { Ingestion } from "@polar-sh/ingestion";
import { S3Strategy } from "@polar-sh/ingestion/strategies/S3";
import { PutObjectCommand, S3Client } from "@aws-sdk/client-s3";

const s3Client = new S3Client({
  region: process.env.AWS_REGION,
  endpoint: process.env.AWS_ENDPOINT_URL,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
});

// Setup the S3 Ingestion Strategy
const s3Ingestion = Ingestion({ accessToken: process.env.POLAR_ACCESS_TOKEN })
  .strategy(new S3Strategy(s3Client))
  .ingest("s3-uploads");

export async function POST(request: Request) {
  try {
    // Get the wrapped S3 Client
    // Pass Customer Id to properly annotate the ingestion events with a specific customer
    const s3 = s3Ingestion.client({
      customerId: request.headers.get("X-Polar-Customer-Id") ?? "",
    });

    await s3.send(
      new PutObjectCommand({
        Bucket: process.env.AWS_BUCKET_NAME,
        Key: "a-random-key",
        Body: JSON.stringify({
          name: "John Doe",
          age: 30,
        }),
        ContentType: "application/json",
      })
    );

    return Response.json({});
  } catch (error) {
    return Response.json({ error: error.message });
  }
}
```

#### Ingestion Payload

```json
{
  "customerId": "123",
  "name": "s3-uploads",
  "metadata": {
    "bytes": 100,
    "bucket": "my-bucket",
    "key": "my-key",
    "contentType": "application/text"
  }
}
```


# Stream Strategy
Source: https://docs.polar.sh/features/usage-based-billing/ingestion-strategies/stream-strategy

Ingestion strategy for Readable & Writable Streams

## Javascript SDK

Wrap any Readable or Writable stream of choice to automatically ingest the bytes consumed.

```
pnpm add @polar-sh/ingestion
```

```typescript
import { Ingestion } from '@polar-sh/ingestion';
import { StreamStrategy } from '@polar-sh/ingestion/strategies/Stream';

const myReadstream = createReadStream(...);

// Setup the Stream Ingestion Strategy
const streamIngestion = Ingestion({ accessToken: process.env.POLAR_ACCESS_TOKEN })
  .strategy(new StreamStrategy(myReadstream))
  .ingest("my-stream");

export async function GET(request: Request) {
  try {

    // Get the wrapped stream
    // Pass Customer Id to properly annotate the ingestion events with a specific customer
    const stream = streamIngestion.client({
      customerId: request.headers.get("X-Polar-Customer-Id") ?? ""
    });

    // Consume stream...
    stream.on('data', () => ...)

    return Response.json({});
  } catch (error) {
    return Response.json({ error: error.message });
  }
}
```

#### Ingestion Payload

```json
{
  "customerId": "123",
  "name": "my-stream",
  "metadata": {
    "bytes": 100
  }
}
```


# Introduction
Source: https://docs.polar.sh/features/usage-based-billing/introduction

Usage based billing using ingested events

<Info>
  Usage Based Billing is a new feature. We have a lot in store and welcome
  feedback!
</Info>

## Overview

Polar has a powerful Usage Based Billing infrastructure that allows you to charge your customers based on the usage of your application.

This is done by ingesting events from your application, creating Meters to represent that usage, and then adding metered prices to Products to charge for it.

## Concepts

### Events

Events are the core of Usage Based Billing. They represent *some* usage done by a customer in your application. Typical examples of events are:

* A customer consumed AI LLM tokens
* A customer streamed minutes of video
* A customer uploaded a file to your application

Events are sent to Polar using the [Events Ingestion API](/api-reference/events/ingest) and are stored in our database. An event consists of the following fields:

* A `name`, which is a string that can be used to identify the type event. For example, `ai_usage`, `video_streamed` or `file_uploaded`.
* A `customer_id` or `external_customer_id`, which is the Polar's customer ID or your user's ID. This is used to identify the customer that triggered the event.
* A `metadata` object, which is a JSON object that can contain any additional information about the event. This is useful for storing information that can be used to filter the events or compute the actual usage. For example, you can store the duration of the video streamed or the size of the file uploaded.

Here is an example of an event:

```json
{
  "name": "ai_usage",
  "external_customer_id": "cus_123",
  "metadata": {
    "model": "gpt-4.1-nano",
    "requests": 1,
    "total_tokens": 77,
    "request_tokens": 58,
    "response_tokens": 19
  }
}
```

### Meters

Meters are there to filter and aggregate the events that are ingested. Said another way, this is how you define what usage you want to charge for, based on the events you send to Polar. For example:

* AI usage meter, which filters the events with the name `ai_usage` and sums the `total_tokens` field.
* Video streaming meter, which filters the events with the name `video_streamed` and sums the `duration` field.
* File upload meter, which filters the events with the name `file_uploaded` and sums the `size` field.

You can create and manage your meters from the dashboard. Polar is then able to compute the usage over time, both globally and per customer.

### Metered Price

A metered price is a price that is based on the usage of a meter, which is computed by filtering aggregating the events that are ingested. This is how you charge your customers for the usage of your application.

### Meter Credits benefit

You can give credits to your customers on a specific meter. This is done by creating a Meter Credits Benefit, which is a special type of benefit that allows you to give credits to your customers on a specific meter.

On a recurring product, the customer will be credited the amount of units specified in the benefit at the beginning of every subscription cycle period — monthly or yearly.

### Diagram

Here is a diagram of how the different components of Usage Based Billing work together:

```mermaid
flowchart TD
App[Your Application]
User[User]

    subgraph Polar["Polar"]
        API[Events Ingestion API]
        DB[(Events database)]
        Meters[Meters]
        Products[Products]
        Benefit[Meter Credits Benefit]

        subgraph MeteredPrice["Metered Prices"]
            Unit[Unit Pricing]
        end
    end


    User -->|Uses| App
    App -->|Sends events| API
    API -->|Stores events| DB
    Benefit -->|Stores credit events| DB

    DB -->|Filtered & aggregated by| Meters
    Meters -->|Associated with| Products
    Benefit -->|Associated with| Meters

    Products -.->|Apply| MeteredPrice
```

## Quickstart

Get up and running in 5 minutes

<Steps>
  <Step title="Enable Usage Based Billing">
    Usage Based Billing is currently in Alpha. Enable it by navigating to the Organization Settings and toggling the "Usage Based Billing" switch.
  </Step>

  <Step title="Create a Meter">
    Meters consist of filters and an aggregation function.
    The filter is used to filter the events that should be included in the meter and the aggregation function is used to compute the usage.

    <img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/usage/create-meter.light.png" />

    <img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/usage/create-meter.dark.png" />
  </Step>

  <Step title="Add metered price to a Product">
    To enable usage based billing for a Product, you need to add a metered price to the Product. Metered prices are only applicable to Subscription Products.

    <img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/usage/product-meter.light.png" />

    <img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/usage/product-meter.dark.png" />
  </Step>

  <Step title="Ingest Events">
    Now you're ready to ingest events from your application. Sending events which match the meter's filter will increment the meter's usage for the customer.

    <img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/usage/ingest.light.png" />

    <img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/usage/ingest.dark.png" />
  </Step>

  <Step title="Customer Usage">
    Customers can view their estimated charges for each meter in the Customer Portal.

    <img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/usage/portal.light.png" />

    <img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/features/usage/portal.dark.png" />
  </Step>
</Steps>


# Meters
Source: https://docs.polar.sh/features/usage-based-billing/meters

Creating and managing meters for Usage Based Billing

Meters are there to filter and aggregate the events that are ingested. Said another way, this is how you define what usage you want to charge for, based on the events you send to Polar. For example:

* AI usage meter, which filters the events with the name `ai_usage` and sums the `total_tokens` field.
* Video streaming meter, which filters the events with the name `video_streamed` and sums the `duration` field.
* File upload meter, which filters the events with the name `file_uploaded` and sums the `size` field.

You can create and manage your meters from the dashboard. Polar is then able to compute the usage over time, both globally and per customer.

## Creating a Meter

To create a meter, navigate to the Meters page in the sidebar and click the "Create Meter" button.

<img className="block dark:hidden" src="https://mintcdn.com/polar/assets/features/usage/create-meter.light.png?maxW=3598&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=277c6d19c062bfa6111c869ffe99d989" width="3598" height="2070" data-path="assets/features/usage/create-meter.light.png" srcset="https://mintcdn.com/polar/assets/features/usage/create-meter.light.png?w=280&maxW=3598&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=a1db4f91a79c09398b902643004a143b 280w, https://mintcdn.com/polar/assets/features/usage/create-meter.light.png?w=560&maxW=3598&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=037ae7b15bf352f5b21fd8fa12add7b1 560w, https://mintcdn.com/polar/assets/features/usage/create-meter.light.png?w=840&maxW=3598&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=e6eafc7cee088dc2605f36d221c448e0 840w, https://mintcdn.com/polar/assets/features/usage/create-meter.light.png?w=1100&maxW=3598&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=a132a54f8854ff44a6ccbd892ecee665 1100w, https://mintcdn.com/polar/assets/features/usage/create-meter.light.png?w=1650&maxW=3598&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=a17247a095d38496312b27d371c2c208 1650w, https://mintcdn.com/polar/assets/features/usage/create-meter.light.png?w=2500&maxW=3598&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=67afc7739622eda53c86406403352746 2500w" data-optimize="true" data-opv="2" />

<img className="hidden dark:block" src="https://mintcdn.com/polar/assets/features/usage/create-meter.dark.png?maxW=3590&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=04a76d84cbd2bf304b8f9673feb8dd2a" width="3590" height="2066" data-path="assets/features/usage/create-meter.dark.png" srcset="https://mintcdn.com/polar/assets/features/usage/create-meter.dark.png?w=280&maxW=3590&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=bbb3eb0d2fb9458d9b92d19104f62b49 280w, https://mintcdn.com/polar/assets/features/usage/create-meter.dark.png?w=560&maxW=3590&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=a8facbcff00849f9358316710c0872b8 560w, https://mintcdn.com/polar/assets/features/usage/create-meter.dark.png?w=840&maxW=3590&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=765b33e38001e06bdecf26f9e6847967 840w, https://mintcdn.com/polar/assets/features/usage/create-meter.dark.png?w=1100&maxW=3590&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=e3289e4be3640f90e2f68a8ccedbc7ce 1100w, https://mintcdn.com/polar/assets/features/usage/create-meter.dark.png?w=1650&maxW=3590&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=3bad9b972e4a8e7088e45d77bbdd1640 1650w, https://mintcdn.com/polar/assets/features/usage/create-meter.dark.png?w=2500&maxW=3590&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=d3b13e9ede04a8e4d1a477b999eb7daa 2500w" data-optimize="true" data-opv="2" />

## Filters

A filter is a set of clauses that are combined using conjunctions. They're used to filter events that you've ingested into Polar.

<img className="block dark:hidden" src="https://mintcdn.com/polar/assets/features/usage/filter.light.png?maxW=1274&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=9c87c28011bae2efece2551c36a5ee77" width="1274" height="922" data-path="assets/features/usage/filter.light.png" srcset="https://mintcdn.com/polar/assets/features/usage/filter.light.png?w=280&maxW=1274&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=6887aead695521a260a046947217ea30 280w, https://mintcdn.com/polar/assets/features/usage/filter.light.png?w=560&maxW=1274&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=9c21fe17b35fc27040874bb3a2b85493 560w, https://mintcdn.com/polar/assets/features/usage/filter.light.png?w=840&maxW=1274&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=321a63fc15bd6c4d5cb524e260700fc8 840w, https://mintcdn.com/polar/assets/features/usage/filter.light.png?w=1100&maxW=1274&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=f54fd6dba29d594ddaa22705dd16191f 1100w, https://mintcdn.com/polar/assets/features/usage/filter.light.png?w=1650&maxW=1274&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=402a75eb0e65857eaff1d1932bac9e71 1650w, https://mintcdn.com/polar/assets/features/usage/filter.light.png?w=2500&maxW=1274&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=994b06743b48c05984774c664efbb21a 2500w" data-optimize="true" data-opv="2" />

<img className="hidden dark:block" src="https://mintcdn.com/polar/assets/features/usage/filter.dark.png?maxW=1276&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=5b7cda77d79b26ece9f7fa383bca4ff7" width="1276" height="914" data-path="assets/features/usage/filter.dark.png" srcset="https://mintcdn.com/polar/assets/features/usage/filter.dark.png?w=280&maxW=1276&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=ca0dfa3edb084552585e8d7c09f9fea6 280w, https://mintcdn.com/polar/assets/features/usage/filter.dark.png?w=560&maxW=1276&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=913db3538ab8f12d8740c04f525e1d4a 560w, https://mintcdn.com/polar/assets/features/usage/filter.dark.png?w=840&maxW=1276&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=89e7babe67fcb9b0c705794b9f0fb911 840w, https://mintcdn.com/polar/assets/features/usage/filter.dark.png?w=1100&maxW=1276&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=5ef2a9164706afe1858dfff159d5f7ea 1100w, https://mintcdn.com/polar/assets/features/usage/filter.dark.png?w=1650&maxW=1276&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=658d4824b9e132f103bfc2c91c5282da 1650w, https://mintcdn.com/polar/assets/features/usage/filter.dark.png?w=2500&maxW=1276&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=957399e0875d693678e04425ec70e275 2500w" data-optimize="true" data-opv="2" />

### Clauses

A clause is a condition that an event must meet to be included in the meter.

#### Property

Properties are the properties of the event that you want to filter on.

If you want to match on a metadata field, you can use the metadata key directly. No need to include a `metadata.` prefix.

#### Operator

Operators are the operators that you want to use to filter the events.

* **Equals**
* **Not equals**
* **Greater Than**
* **Greater Than or Equals**
* **Less Than**
* **Less Than or Equals**
* **Contains**
* **Does Not Contain**

#### Value

Values are automatically parsed in the filter builder. They're parsed in the following order:

1. Number — Tries to parse the value as number
2. Boolean — Checks if value is "true" or "false"
3. String — Treats value as string as fallback

### Conjunctions

A conjunction is a logical operator that combines two or more clauses.

* **and** — All clauses must be true for the event to be included.
* **or** — At least one clause must be true for the event to be included.

## Aggregation

The aggregation is the function that is used to aggregate the events that match the filter.

For example, if you want to count the number of events that match the filter, you can use the **Count** aggregation. If you want to sum the value of a metadata field, you can use the **Sum** aggregation.

* **Count** — Counts the number of events that match the filter.
* **Sum** — Sums the value of a property.
* **Average** — Computes the average value of a property.
* **Minimum** — Computes the minimum value of a property.
* **Maximum** — Computes the maximum value of a property.
* **Unique** — Counts the number of unique values of a property.

<AccordionGroup>
  <Accordion title="Example">
    Consider the following events:

    ```json
    [
      {
        "name": "ai_usage",
        "external_customer_id": "cus_123",
        "metadata": {
          "total_tokens": 10
        }
      },
      {
        "name": "ai_usage",
        "external_customer_id": "cus_123",
        "metadata": {
          "total_tokens": 20
        }
      },
      {
        "name": "ai_usage",
        "external_customer_id": "cus_123",
        "metadata": {
          "total_tokens": 30
        }
      },
      {
        "name": "ai_usage",
        "external_customer_id": "cus_123",
        "metadata": {
          "total_tokens": 30
        }
      }
    ]
    ```

    Here is the result of each aggregation function, over the `total_tokens` metadata property:

    * **Count**: 4 units
    * **Sum**: 90 units
    * **Average**: 22.5 units
    * **Minimum**: 10 units
    * **Maximum**: 30 units
    * **Unique**: 3 units
  </Accordion>
</AccordionGroup>

If you want to use a metadata property in the aggregation, you can use the metadata property directly. No need to include a `metadata.` prefix.

## Example

The following Meter Filter & Aggregation will match events that have the name `openai-usage` and sum units over metadata property `completionTokens`.

<img className="block dark:hidden" src="https://mintcdn.com/polar/assets/features/usage/meter.light.png?maxW=1108&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=c2a4f33acab78c0a3edea6d52e3e0ae4" width="1108" height="936" data-path="assets/features/usage/meter.light.png" srcset="https://mintcdn.com/polar/assets/features/usage/meter.light.png?w=280&maxW=1108&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=71371a3256c0a7c96c3a7ad1e9cd591a 280w, https://mintcdn.com/polar/assets/features/usage/meter.light.png?w=560&maxW=1108&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=fbdee45748b6cf6bf7e29aff5dc1f0ce 560w, https://mintcdn.com/polar/assets/features/usage/meter.light.png?w=840&maxW=1108&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=04a2f14534f13d795790b3282622f12a 840w, https://mintcdn.com/polar/assets/features/usage/meter.light.png?w=1100&maxW=1108&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=6cb689e595c3c5e866df71115d4292da 1100w, https://mintcdn.com/polar/assets/features/usage/meter.light.png?w=1650&maxW=1108&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=b250f366a9fd2de49462a07e09d60647 1650w, https://mintcdn.com/polar/assets/features/usage/meter.light.png?w=2500&maxW=1108&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=02f9f021901791a994c7ea29aca195cb 2500w" data-optimize="true" data-opv="2" />

<img className="hidden dark:block" src="https://mintcdn.com/polar/assets/features/usage/meter.dark.png?maxW=1116&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=7237fa8ddb22065c0b5aff22399ed08a" width="1116" height="928" data-path="assets/features/usage/meter.dark.png" srcset="https://mintcdn.com/polar/assets/features/usage/meter.dark.png?w=280&maxW=1116&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=ce5f1bbe889543151d267cf28d4a3ff9 280w, https://mintcdn.com/polar/assets/features/usage/meter.dark.png?w=560&maxW=1116&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=c92252b49ce75b7f77eec082d0aeda70 560w, https://mintcdn.com/polar/assets/features/usage/meter.dark.png?w=840&maxW=1116&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=c92eb88ae2c185168079b576fd12ed98 840w, https://mintcdn.com/polar/assets/features/usage/meter.dark.png?w=1100&maxW=1116&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=d1635a876032c45b2125945786bd9a7e 1100w, https://mintcdn.com/polar/assets/features/usage/meter.dark.png?w=1650&maxW=1116&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=8974d73718672640ed19639519741687 1650w, https://mintcdn.com/polar/assets/features/usage/meter.dark.png?w=2500&maxW=1116&auto=format&n=fvZ8W8dEYCC0WMfg&q=85&s=305c63272455246e022ea5a01d37bfaa 2500w" data-optimize="true" data-opv="2" />

<Tip>
  You can **Preview** the events matched by the meter while creating it.
</Tip>

## Good to know

A few things to keep in mind when creating and managing meters:

### Renaming a Meter

Until [https://github.com/polarsource/polar/issues/6490](https://github.com/polarsource/polar/issues/6490) is resolved, please [contact support](/support) to help you with renaming the meter.

### Updating a Meter

You may update a meter's filters or aggregation function as long as the meter doesn't have any processed events.

### Deleting a Meter

Meters are permanent. Once created, they cannot be deleted.


# Integrate Polar with Laravel
Source: https://docs.polar.sh/guides/laravel

In this guide, we'll show you how to integrate Polar with Laravel.

<img src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/guides/laravel/hero.jpeg" />

Consider following this guide while using the Polar Sandbox Environment. This will allow you to test your integration without affecting your production data.

## Polar Laravel Example App

We've created a simple example Laravel application that you can use as a reference.

[View Code on GitHub](https://github.com/polarsource/polar-laravel)

## Setting up environment variables

### Polar API Key

To authenticate with Polar, you need to create an access token, and supply it to Laravel using a `POLAR_API_KEY` environment variable.

You can create an organization access token from your organization settings.

## Fetching Polar Products for display

### Creating the Products Controller

Go ahead and add the following entry in your `routes/web.php` file:

```php
// routes/web.php
Route::get('/products', [ProductsController::class, 'handle']);
```

Next up, create the `ProductsController` class in the `app/Http/Controllers` directory:

```php
// app/Http/Controllers/ProductsController.php
<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ProductsController extends Controller
{
    public function handle(Request $request)
    {
        // Change from sandbox-api.polar.sh -> api.polar.sh when ready to go live
        // And don't forget to update the .env file with the correct POLAR_ORGANIZATION_ID and POLAR_WEBHOOK_SECRET
        $data = Http::get('https://sandbox-api.polar.sh/v1/products', [
            'is_archived' => false,
        ]);

        $products = $data->json();

        return view('products', ['products' => $products['items']]);
    }
}
```

## Displaying Products

Finally, create the `products` view in the `resources/views` directory:

```php
// resources/views/products.blade.php
@foreach ($products as $product)
    <div>
        <h3>{{ $product['name'] }}</h3>
        <a href="/checkout?priceId={{ $product['prices'][0]['id'] }}">Buy</a>
    </div>
@endforeach
```

Notice that we create a link to `/checkout` with a query parameter `priceId`. This is the ID of the price that the user will be charged for when they click the "Buy" button. We will configure this route in the next section.

That's it for the products page. You can now display the products to your users, and they will be able to buy them. Let's now create the checkout endpoint.

## Generating Polar Checkout Sessions

This endpoint will be responsible for creating a new checkout session, redirecting the user to the Polar Checkout page & redirect back to a configured confirmation page.

Go ahead and create a new entry in your `routes/web.php` file:

```php
// routes/web.php
Route::get('/checkout', [CheckoutController::class, 'handle']);
```

Next, create the `CheckoutController` class in the `app/Http/Controllers` directory:

```php
// app/Http/Controllers/CheckoutController.php
<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CheckoutController extends Controller
{
    public function handle(Request $request)
    {
        $productPriceId = $request->query('priceId', '');
        // Polar will replace {CHECKOUT_ID} with the actual checkout ID upon a confirmed checkout
        $confirmationUrl = $request->getSchemeAndHttpHost() . '/confirmation?checkout_id={CHECKOUT_ID}';

        // Change from sandbox-api.polar.sh -> api.polar.sh when ready to go live
        // And don't forget to update the .env file with the correct POLAR_ORGANIZATION_ID and POLAR_WEBHOOK_SECRET
        $result = Http::withHeaders([
            'Authorization' => 'Bearer ' . env('POLAR_API_KEY'),
            'Content-Type' => 'application/json',
        ])->post('https://sandbox-api.polar.sh/v1/checkouts/custom/', [
            'product_price_id' => $productPriceId,
            'success_url' => $confirmationUrl,
            'payment_processor' => 'stripe',
        ]);

        $data = $result->json();

        $checkoutUrl = $data['url'];

        return redirect($checkoutUrl);
    }
}
```

We can now easily create a checkout session & redirect there by creating a link to `/checkout?priceId={priceId}`. Just like we did when displaying the products above.

Upon Checkout success, the user will be redirected to the confirmation page.

## Creating the Confirmation Page

Create a new entry in your `routes/web.php` file:

```php
// routes/web.php
Route::get('/confirmation', [ConfirmationController::class, 'handle']);
```

Next, create the `ConfirmationController` class in the `app/Http/Controllers` directory:

```php
// app/Http/Controllers/ConfirmationController.php
<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ConfirmationController extends Controller
{
    public function handle(Request $request)
    {
        // Change from sandbox-api.polar.sh -> api.polar.sh when ready to go live
        // And don't forget to update the .env file with the correct POLAR_ORGANIZATION_ID and POLAR_WEBHOOK_SECRET
        $data = Http::withHeaders([
            'Authorization' => 'Bearer ' . env('POLAR_API_KEY'),
            'Content-Type' => 'application/json',
        ])->get('https://sandbox-api.polar.sh/v1/checkouts/custom/' . $request->query('checkout_id'));

        $checkout = $data->json();

        Log::info(json_encode($checkout, JSON_PRETTY_PRINT));

        return view('confirmation', ['checkout' => $checkout]);
    }
}
```

The checkout is not considered "successful" yet however. It's initially marked as `confirmed` until you've received a webhook event `checkout.updated` with a status set to `succeeded`. We'll cover this in the next section.

## Handling Polar Webhooks

Polar can send you events about various things happening in your organization. This is very useful for keeping your database in sync with Polar checkouts, orders, subscriptions, etc.

Configuring a webhook is simple. Head over to your organization's settings page and click on the "Add Endpoint" button to create a new webhook.

### Tunneling webhook events to your local development environment

If you're developing locally, you can use a tool like [ngrok](https://ngrok.com/) to tunnel webhook events to your local development environment. This will allow you to test your webhook handlers without deploying them to a live server.

Run the following command to start an ngrok tunnel:

```bash
ngrok http 3000
```

### Add Webhook Endpoint

1. Point the Webhook to `your-app.com/api/webhook/polar`. This must be an absolute URL which Polar can reach. If you use ngrok, the URL will look something like this: `https://<your-ngrok-id>.ngrok-free.app/api/webhook/polar`.
2. Select which events you want to be notified about. You can read more about the available events in the [Events section](/api-reference#webhooks).
3. Generate a secret key to sign the requests. This will allow you to verify that the requests are truly coming from Polar.
4. Add the secret key to your environment variables.

```bash
# .env
POLAR_API_KEY="polar_oat..."
POLAR_WEBHOOK_SECRET="..."
```

### Setting up the Webhook handler

First, we need to install the standard-webhooks package to properly decode the incoming webhook payloads.

```bash
composer require standard-webhooks/standard-webhooks:dev-main
```

Go and add a `routes/api.php` file and add the following entry:

```php
// routes/api.php
Route::webhooks('/webhook/polar');
```

Make sure that it is included in the Bootstrap file.

```php
// bootstrap/app.php
<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        //
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
```

We will use Spatie's Webhook Client to handle the webhook events. It will automatically verify the signature of the requests, and dispatch the payload to a job queue for processing.

```bash
composer require spatie/laravel-webhook-client
```

Let's publish the config:

```bash
php artisan vendor:publish --provider="Spatie\WebhookClient\WebhookClientServiceProvider" --tag="webhook-client-config"
```

This will create a new file called webhook-client.php in the config folder.

We need to adjust it to properly verify the signature of the requests.

```php
// config/webhook-client.php
<?php
return [
    'configs' => [
        [
            /*
             * This package supports multiple webhook receiving endpoints. If you only have
             * one endpoint receiving webhooks, you can use 'default'.
             */
            'name' => 'default',

            /*
             * We expect that every webhook call will be signed using a secret. This secret
             * is used to verify that the payload has not been tampered with.
             */
            'signing_secret' => env('POLAR_WEBHOOK_SECRET'),

            /*
             * The name of the header containing the signature.
             */
            'signature_header_name' => 'webhook-signature',

            /*
             *  This class will verify that the content of the signature header is valid.
             *
             * It should implement \Spatie\WebhookClient\SignatureValidator\SignatureValidator
             */
            // 'signature_validator' => \Spatie\WebhookClient\SignatureValidator\DefaultSignatureValidator::class,
            'signature_validator' => App\Handler\PolarSignature::class,

            /*
             * This class determines if the webhook call should be stored and processed.
             */
            'webhook_profile' => \Spatie\WebhookClient\WebhookProfile\ProcessEverythingWebhookProfile::class,

            /*
             * This class determines the response on a valid webhook call.
             */
            'webhook_response' => \Spatie\WebhookClient\WebhookResponse\DefaultRespondsTo::class,

            /*
             * The classname of the model to be used to store webhook calls. The class should
             * be equal or extend Spatie\WebhookClient\Models\WebhookCall.
             */
            'webhook_model' => \Spatie\WebhookClient\Models\WebhookCall::class,

            /*
             * In this array, you can pass the headers that should be stored on
             * the webhook call model when a webhook comes in.
             *
             * To store all headers, set this value to `*`.
             */
            'store_headers' => [],

            /*
             * The class name of the job that will process the webhook request.
             *
             * This should be set to a class that extends \Spatie\WebhookClient\Jobs\ProcessWebhookJob.
             */
            'process_webhook_job' => App\Handler\ProcessWebhook::class,
        ],
    ],

    /*
     * The integer amount of days after which models should be deleted.
     *
     * 7 deletes all records after 1 week. Set to null if no models should be deleted.
     */
    'delete_after_days' => 30,
];
```

### Preparing the database

By default, all webhook calls get saved into the database. So, we need to publish the migration that will hold the records. So run:

```bash
php artisan vendor:publish --provider="Spatie\WebhookClient\WebhookClientServiceProvider" --tag="webhook-client-migrations"
```

This will create a new migration file in the “database/migration” folder.

Then run `php artisan migrate` to run the migration.

### Setting up the queue system

Before we set up our job handler — let’s set up our queue system

Go to your “.env” file and set the QUEUE\_CONNECTION=database — you can decide to use other connections like redis.

Let’s create our jobs table by running php artisan queue:table and then run the migration using php artisan migrate.

### Create the Handlers

The next thing we do is to create a folder named Handler inside the app folder. Then inside this app/Handler, create two files which are

* PolarSignature.php
* ProcessWebhook.php

Inside app/Handler/PolarSignature.php, what we want to do is to validate that the request came from Polar. Add the code to that file.

```php
// app/Handler/PolarSignature.php
<?php

namespace App\Handler;

use Illuminate\Http\Request;
use Spatie\WebhookClient\Exceptions\WebhookFailed;
use Spatie\WebhookClient\WebhookConfig;
use Spatie\WebhookClient\SignatureValidator\SignatureValidator;

class PolarSignature implements SignatureValidator
{
    public function isValid(Request $request, WebhookConfig $config): bool
    {
        $signingSecret = base64_encode($config->signingSecret);
        $wh = new \StandardWebhooks\Webhook($signingSecret);

        return boolval( $wh->verify($request->getContent(), array(
            "webhook-id" => $request->header("webhook-id"),
            "webhook-signature" => $request->header("webhook-signature"),
            "webhook-timestamp" => $request->header("webhook-timestamp"),
        )));
    }
}
```

Great. So the other file app/Handler/ProcessWebhook.php extends the ProcessWebhookJob class which holds the WebhookCall variables containing each job’s detail.

```php
// app/Handler/ProcessWebhook.php
<?php

namespace App\Handler;

use Illuminate\Support\Facades\Log;
use Spatie\WebhookClient\Jobs\ProcessWebhookJob;

class ProcessWebhook extends ProcessWebhookJob
{
    public function handle()
    {
        $decoded = json_decode($this->webhookCall, true);
        $data = $decoded['payload'];

        switch ($data['type']) {
            case "checkout.created":
                // Handle the checkout created event
                break;
            case "checkout.updated":
                // Handle the checkout updated event
                break;
            case "subscription.created":
                // Handle the subscription created event
                break;
            case "subscription.updated":
                // Handle the subscription updated event
                break;
            case "subscription.active":
                // Handle the subscription active event
                break;
            case "subscription.revoked":
                // Handle the subscription revoked event
                break;
            case "subscription.canceled":
                // Handle the subscription canceled event
                break;
            default:
                // Handle unknown event
                Log::info($data['type']);
                break;
        }

        //Acknowledge you received the response
        http_response_code(200);
    }
}
```

Our application is ready to receive webhook requests.

Don’t forget to run `php artisan queue:listen` to process the jobs.

### Tips

If you're keeping track of active and inactive subscriptions in your database, make sure to handle the `subscription.active` and `subscription.revoked` events accordingly.

The cancellation of a subscription is handled by the `subscription.canceled`
event. The user has probably canceled their subscription before the end of the
billing period. Do not revoke any kind of access immediately, but rather wait
until the end of the billing period or when you receive the
`subscription.revoked` event.

## Notifying the client about the event

If you're building a real-time application, you might want to notify the client about the event. On the confirmation-page, you can listen for the `checkout.updated` event and update the UI accordingly when it reaches the succeeded status.

## Polar Laravel Example App

We've created a simple example Laravel application that you can use as a reference

[View Code on GitHub](https://github.com/polarsource/polar-laravel)

If you have issues or need support, feel free to join [our Discord](https://discord.gg/Pnhfz3UThd).


# Integrate Polar with Next.js
Source: https://docs.polar.sh/guides/nextjs

In this guide, we'll show you how to integrate Polar with Next.js.

Feel free to use our quick-start script to get started inside a new Next.js project:

```bash
# Inside a new Next.js project
npx polar-init
```

Consider following this guide while using the Polar Sandbox Environment. This will allow you to test your integration without affecting your production data.

[A complete code-example of this guide can be found on GitHub](https://github.com/polarsource/polar-next).

## Install the Polar JavaScript SDK

To get started, you need to install the Polar JavaScript SDK and the Polar Nextjs helper package. You can do this by running the following command:

```bash
pnpm install @polar-sh/sdk @polar-sh/nextjs
```

## Setting up environment variables

### Polar Access Token

To authenticate with Polar, you need to create an access token, and supply it to Next.js using a `POLAR_ACCESS_TOKEN` environment variable.

You can create an organization access token from your organization settings.

## Configuring a Polar API Client

To interact with the Polar API, you need to create a new instance of the `Polar` class. This class uses the provided access token to authenticate with the Polar API.

```typescript
// src/polar.ts
import { Polar } from "@polar-sh/sdk";

export const api = new Polar({
  accessToken: process.env.POLAR_ACCESS_TOKEN!,
  server: "sandbox", // Use this option if you're using the sandbox environment - else use 'production' or omit the parameter
});
```

Remember to replace `sandbox` with `production` when you're ready to switch to the production environment.

## Generating Polar Checkout Sessions

Next up, we need to create a checkout endpoint to handle the creation of checkout sessions.

Go ahead and create a new GET route in Next.js.

```typescript
// src/app/checkout/route.ts
import { Checkout } from "@polar-sh/nextjs";

export const GET = Checkout({
  accessToken: process.env.POLAR_ACCESS_TOKEN!,
  successUrl: "/confirmation?checkout_id={CHECKOUT_ID}",
  server: "sandbox", // Use this option if you're using the sandbox environment - else use 'production' or omit the parameter
});
```

## Handling Polar Webhooks

Polar can send you events about various things happening in your organization. This is very useful for keeping your database in sync with Polar checkouts, orders, subscriptions, etc.

Configuring a webhook is simple. Head over to your organization's settings page and click on the "Add Endpoint" button to create a new webhook.

### Tunneling webhook events to your local development environment

If you're developing locally, you can use a tool like [ngrok](https://ngrok.com/) to tunnel webhook events to your local development environment. This will allow you to test your webhook handlers without deploying them to a live server.

Run the following command to start an ngrok tunnel:

```bash
ngrok http 3000
```

### Add Webhook Endpoint

1. Point the Webhook to `your-app.com/api/webhook/polar`. This must be an absolute URL which Polar can reach. If you use ngrok, the URL will look something like this: `https://<your-ngrok-id>.ngrok-free.app/api/webhook/polar`.
2. Select which events you want to be notified about. You can read more about the available events in the [Events section](/api-reference#webhooks).
3. Generate a secret key to sign the requests. This will allow you to verify that the requests are truly coming from Polar.
4. Add the secret key to your environment variables.

```bash
# .env
POLAR_ACCESS_TOKEN="polar_pat..."
POLAR_WEBHOOK_SECRET="..."
```

### Setting up the Webhook handler

```typescript
// src/app/api/webhook/polar/route.ts
import { Webhooks } from "@polar-sh/nextjs";

export const POST = Webhooks({
	webhookSecret: process.env.POLAR_WEBHOOK_SECRET,
	onPayload: async (payload) => // Handle payload...
});
```

The webhook event is now verified and you can proceed to handle the payload data.

### Handling Webhook Events

Depending on which events you've subscribed to, you'll receive different payloads. This is where you can update your database, send notifications, etc.

```typescript
// src/app/api/webhook/polar/route.ts
import { Webhooks } from "@polar-sh/nextjs";

export const POST = Webhooks({
  webhookSecret: process.env.POLAR_WEBHOOK_SECRET,
  onPayload: async (payload) => ...,
  onOrderCreated: async (order) => ...,
  onCustomerStateChanged: async (customerState) => ...,
  ...
});
```

### Notifying the client about the event

If you're building a real-time application, you might want to notify the client about the event. On the confirmation-page, you can listen for the `checkout.updated` event and update the UI accordingly when it reaches the succeeded status.

## Conclusion

If you have issues or need support, feel free to join [our Discord](https://discord.gg/Pnhfz3UThd).


# Authentication
Source: https://docs.polar.sh/integrate/authentication



<Info>
  All bearer tokens should be kept private and never shared or exposed in client-side code.
</Info>

To authenticate requests, Polar API has two mechanisms.

1. [Organization Access Tokens (OAT)](/integrate/oat) - Recommended
2. [OAuth 2.0 Provider](/integrate/oauth2/introduction) (Partner Integrations)

## Organization Access Tokens (OAT)

They are tied to **one** of your organization. You can create them from your organization settings.

## Security

To protect your data and ensure the security of Polar, we've several mechanisms in place to automatically revoke tokens that may have been leaked publicly on the web.

In particular, we're part of the [GitHub Secret Scanning Program](https://docs.github.com/en/code-security/secret-scanning/about-secret-scanning). If GitHub systems detect a Polar token in a code repository or public discussion, our systems are notified and the tokens are immediately revoked.

If you received an email about one of your token being leaked, it means that we were notified of such situation. The email contains the details about the nature of the token and the source of the leak.

In the future, it's crucial that you remain extra cautious about not leaking your tokens publicly online. You can read more about the good practices to manage secrets in the [OWASP Secrets Management Cheat Sheet](https://cheatsheetseries.owasp.org/cheatsheets/Secrets_Management_Cheat_Sheet.html).


# Customer State
Source: https://docs.polar.sh/integrate/customer-state

The quickest way to integrate billing in your application

Customer State is a concept allowing you to query for the current state of a customer, including their active subscriptions and granted [benefits](/features/benefits/introduction), in a single [API call](/api-reference/customers/state-external) or single [webhook event](/api-reference/webhooks/customer.state_changed).

Combined with the [External ID](/features/customer-management#external-id) feature, you can get up-and-running in minutes.

## The customer state object

The customer state object contains:

* All the data about the customer.
* The list of their **active** subscriptions.
* The list of their **granted** benefits.
* The list of their **active** meters, with their current balance.

Thus, with that single object, you have all the required information to check if you should provision access to your service or not.

<Card title="Get Customer State by External ID" icon="ring" iconType="duotone" href="/api-reference/customers/state-external" horizontal>
  One endpoint to rule them all, using your own customer ID.
</Card>

<Card title="Get Customer State " icon="ring" iconType="duotone" href="/api-reference/customers/state" horizontal>
  The same one, but with internal Polar customer ID.
</Card>

## The `customer.state_changed` webhook

To be notified of the customer state changes, you can listen to the `customer.state_changed` webhook event. It's triggered when:

* Customer is created, updated or deleted.
* A subscription is created or updated.
* A benefit is granted or revoked.

By subscribing to this webhook event, you keep your system up-to-date and update your customer's access accordingly.

<Card title="customer.state_changed" icon="ring" iconType="duotone" href="/api-reference/webhooks/customer.state_changed" horizontal>
  One webhook to rule them all.
</Card>


# Polar as Model Context Protocol (MCP)
Source: https://docs.polar.sh/integrate/mcp

Extend the capabilities of your AI Agents with Polar as MCP Server

<img src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/integrate/mcp/mcp.png" />

Supercharge your AI Agents with Polar as a Model Context Protocol (MCP) server.

## What is MCP?

MCP is a protocol for integrating tools with AI Agents. It can greatly enhance the capabilities of your AI Agents by providing them with real-time data and context.

Polar has MCP support built into the Polar TypeScript SDK.

## How does it work?

You need a MCP-capable Agent environment to use Polar as an MCP server. A few of them are Claude and Cursor.

## Using Polar as an MCP server

### Claude

Add the following server definition to your claude\_desktop\_config.json file:

```json
{
  "mcpServers": {
    "Polar": {
      "command": "npx",
      "args": [
        "-y",
        "--package",
        "@polar-sh/sdk",
        "--",
        "mcp",
        "start",
        "--access-token",
        "..."
      ]
    }
  }
}
```

### Cursor

Go to Cursor Settings > Features > MCP Servers > Add new MCP server and use the following settings:

* Name: Polar
* Type: command
* Command:

```bash
npx -y --package @polar-sh/sdk -- mcp start --access-token ...
```

### Help

For a full list of server arguments, run:

```bash
npx -y --package @polar-sh/sdk -- mcp start --help
```


# OAuth 2.0 Connect
Source: https://docs.polar.sh/integrate/oauth2/connect



## Authorize

To start the authorization flow you need to redirect the user to the authorization URL. It looks like this:

```
https://polar.sh/oauth2/authorize?
  response_type=code
  &client_id=CLIENT_ID
  &redirect_uri=https%3A%2F%2Fexample.com%2Fcallback
  &scope=openid%20email
```

The parameters are the one described in the [OpenID Connect specification](https://openid.net/specs/openid-connect-core-1_0.html#AuthRequest). The most important ones are:

<ParamField path="response_type=code" type="string" required>
  Indicates that you want to use the authorization code flow. Most common and
  the only one supported by Polar.
</ParamField>

<ParamField path="client_id" type="string" required>
  The Client ID you got when creating the OAuth 2.0 client.
</ParamField>

<ParamField path="redirect_uri" type="string" required>
  The URL where the user will be redirected after granting access to their data. Make sure you declared it when creating the OAuth2 client.
</ParamField>

<ParamField path="scope" type="string" required>
  A space-separated list of scopes you want to ask for. Make sure they are part of the scopes you declared when creating the OAuth2 client.
</ParamField>

If you redirect the user to this URL, they'll see a page asking them to grant access to their data, corresponding to the scopes you asked for.

<img src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/integrate/oauth2/connect.png" />

If they allow it, they'll be redirected to your `redirect_uri` with a `code` parameter in the query string. This code is a one-time code that you can exchange for an access token.

#### Exchange code token

Once you have the authorization code, you can exchange it for an access token. To do so, you'll need to make a `POST` request to the token endpoint. This call needs to be authenticated with the Client ID and Client Secret you got when creating the OAuth2 client.

Here is an example with cURL:

```bash
curl -X POST https://api.polar.sh/v1/oauth2/token \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -d 'grant_type=authorization_code&code=AUTHORIZATION_CODE&client_id=CLIENT_ID&client_secret=CLIENT_SECRET&redirect_uri=https://example.com/callback'
```

You should get the following response:

```json
{
  "token_type": "Bearer",
  "access_token": "polar_at_XXX",
  "expires_in": 864000,
  "refresh_token": "polar_rt_XXX",
  "scope": "openid email",
  "id_token": "ID_TOKEN"
}
```

The `access_token` will allow you to make authenticated API requests on behalf of the user. The `refresh_token` is a long-lived token that you can use to get new access tokens when the current one expires. The `id_token` is a signed JWT token containing information about the user, as per the [OpenID Connect specification](https://openid.net/specs/openid-connect-core-1_0.html#IDToken).

#### User vs Organization Access Tokens

We support the concept of access tokens at **organization level**. Contrary to the standard access tokens, those tokens are not tied to a user but to an organization. They can be used to make requests operating only on a specific organization data, improving privacy and security.

To ask for an organization access token, you need to add the parameter `sub_type=organization` to the authorization URL:

```
https://polar.sh/oauth2/authorize?response_type=code&client_id=polar_ci_j3X95_MgfdSCeCd2qkFnUw&redirect_uri=https%3A%2F%2Fexample.com%2Fcallback&scope=openid%20email&sub_type=organization
```

At this point, the user will be prompted to select one of their organization before allowing access to their data.

The rest of the flow remains unchanged. The access token you'll get will be tied to the selected organization.

Bear in mind that some endpoints might not support organization access tokens.
Typically, user-specific endpoints like `/v1/users/benefit` will not work with
organization access tokens.

#### Public Clients

Public clients are clients where the Client Secret can't be kept safe, as it would be accessible by the final user. This is the case for SPA, mobile applications, or any client running on the user's device.

In this case, **and only if the client is configured as a Public Client**, the request to the token endpoint won't require the `client_secret` parameter. However, the [PKCE](https://oauth.net/2/pkce/) method will be required to maximize security.

### Make authenticated requests

Once you have an access token, either from a Personal Access Token or from the OpenID Connect flow, you can make authenticated requests to the API. Here is a simple example with cURL:

```bash
curl -X GET https://api.polar.sh/v1/oauth2/userinfo \
  -H 'Authorization: Bearer polar_at_XXX'
```


# Introduction
Source: https://docs.polar.sh/integrate/oauth2/introduction

For partners building services and extensions for Polar customers

### OpenID Connect (OAuth2)

Only use our **OpenID Connect** in case you want to act on the behalf of other users via our API, e.g building an app/service for Polar customers. Otherwise, always use an **Organization Access Token (OAT)** to integrate Polar for your own service.

Polar implements the [OpenID Connect specification](https://openid.net/developers/how-connect-works/) to enable third-party authentication. It's a layer on top of the OAuth2 framework aiming at making integration more standard and predictable.

In particular, it comes with a **discovery endpoint** allowing compatible clients to automatically work with the OpenID Connect server. Here is Polar's one:

[OpenID Configuration](https://api.polar.sh/.well-known/openid-configuration)


# Create an OAuth 2.0 Client
Source: https://docs.polar.sh/integrate/oauth2/setup



Before being able to make authentication requests, you'll need an **OAuth2 Client**. It's the entity that'll identify you, as a third-party developer, between Polar and the final user.

You can manage them from your [User Settings](https://polar.sh/settings#oauth)

Here are the required fields:

* *Application Name*: the name of the application that'll be shown to the final users.
* *Client Type*: the type of client you are creating. [Read more](#public-clients)
* *Redirect URIs*: for security reasons, you need to declare your application URL where the users will be redirected after granting access to their data.
  <Note>
    When configuring your OAuth client, you must use an `https://` URL for security reasons. We block `http://` URLs, except when the hostname is `localhost`. This exception allows you to use `http://localhost` for convenient testing in development mode.
  </Note>
* *Scopes*: the list of scopes your app will be able to ask for. To improve privacy and security, select only the scopes you really need for your application.
* *Homepage URL*: the URL of your application. It'll be shown to the final users on the authorization page.

Optionally, you can also add a **logo**, **terms of service** and **privacy policy** URL. They'll all be shown to the final users on the authorization page.

Once your client is created, you'll get a **Client ID** and a **Client Secret**. You'll need those values to make authentication requests.

Those values are super sensitive and should be kept secret. They allow making authentication requests on Polar!


# Sandbox Environment
Source: https://docs.polar.sh/integrate/sandbox



A separate environment, isolated from your production data

To test Polar or work on your integration without worrying about actual money processing or breaking your live organization, you can use our [sandbox environment](https://sandbox.polar.sh/start).

It's a dedicated server, completely isolated from the production instance where you can do all the experiments you want.

<Note>
  **Why a dedicated environment instead of a test mode?**

  Since we're dealing with money and need to keep track of all movements to assure our Merchant of Record service, we found it safer to isolate live data from test data so it never interferes. Besides, it allows you to create an unlimited number of account and organization to test lot of different scenarios. Consider it as your own development server!
</Note>

## Get started

You can access the sandbox environment directly on [sandbox.polar.sh](https://sandbox.polar.sh/start) or by clicking on `Go to sandbox` from the organization switcher.

You'll then need to create a dedicated user account and organization, the same way described in our [Quick Start guide](/introduction).

### Testing payments

The sandbox environment allows you to experience the complete customer funnel, including checkout. You can perform test payments using Stripe's [test card numbers](https://docs.stripe.com/testing#cards).

The easiest one to test a successful payment is to use the following card number with a future expiration date and random CVC:

```
************** 4242
```

## API and SDK

To make requests to our [API](/api-reference), all you need to do is to switch the base URL from `https://api.polar.sh` to `https://sandbox-api.polar.sh`. You'll also need to create an access token in the **sandbox environment**, the access token created in the production environment can't be used in the sandbox.

Our official SDK supports the sandbox environment through a dedicated parameter.

<CodeGroup>
  ```ts TypeScript
  const polar = new Polar({
    server: 'sandbox',
    accessToken: process.env['POLAR_ACCESS_TOKEN'] ?? '',
  })
  ```

  ```py Python
  s = Polar(
      server="sandbox",
      access_token="<YOUR_BEARER_TOKEN_HERE>",
  )
  ```

  ```go Go
    s := polargo.New(
    	polargo.WithServer("sandbox"),
    	polargo.WithSecurity(os.Getenv("POLAR_ACCESS_TOKEN")),
    )
  ```

  ```php PHP
  $sdk = Polar\Polar::builder()
      ->setServer('sandbox')
      ->setSecurity(
          '<YOUR_BEARER_TOKEN_HERE>'
      )
      ->build();
  ```
</CodeGroup>

## Limitations

The limitations listed below only apply to sandbox and doesn't reflect the behavior in production.

* Subscriptions created in sandbox are automatically canceled **90 days after their creation**.


# Astro
Source: https://docs.polar.sh/integrate/sdk/adapters/astro

Payments and Checkouts made dead simple with Astro

`pnpm install @polar-sh/astro zod`

## Checkout

Create a Checkout handler which takes care of redirections.

```typescript
import { Checkout } from "@polar-sh/astro";
import { POLAR_ACCESS_TOKEN, POLAR_SUCCESS_URL } from "astro:env/server";

export const GET = Checkout({
  accessToken: POLAR_ACCESS_TOKEN,
  successUrl: POLAR_SUCCESS_URL,
  server: "sandbox", // Use sandbox if you're testing Polar - omit the parameter or pass 'production' otherwise
  theme: "dark", // Enforces the theme - System-preferred theme will be set if left omitted
});
```

### Query Params

Pass query params to this route.

* products `?products=123`
* customerId (optional) `?products=123&customerId=xxx`
* customerExternalId (optional) `?products=123&customerExternalId=xxx`
* customerEmail (optional) `?products=123&customerEmail=<EMAIL>`
* customerName (optional) `?products=123&customerName=Jane`
* metadata (optional) `URL-Encoded JSON string`

## Customer Portal

Create a customer portal where your customer can view orders and subscriptions.

```typescript
import { CustomerPortal } from "@polar-sh/astro";
import { POLAR_ACCESS_TOKEN } from "astro:env/server";

export const GET = CustomerPortal({
  accessToken: POLAR_ACCESS_TOKEN,
  getCustomerId: (event) => "", // Function to resolve a Polar Customer ID
  server: "sandbox", // Use sandbox if you're testing Polar - omit the parameter or pass 'production' otherwise
});
```

## Webhooks

A simple utility which resolves incoming webhook payloads by signing the webhook secret properly.

```typescript
import { Webhooks } from '@polar-sh/astro';
import { POLAR_WEBHOOK_SECRET } from "astro:env/server"

export const POST = Webhooks({
  webhookSecret: POLAR_WEBHOOK_SECRET,
  onPayload: async (payload) => /** Handle payload */,
})
```

### Payload Handlers

The Webhook handler also supports granular handlers for easy integration.

* `onPayload` - Catch-all handler for any incoming Webhook event
* `onCheckoutCreated` - Triggered when a checkout is created
* `onCheckoutUpdated` - Triggered when a checkout is updated
* `onOrderCreated` - Triggered when an order is created
* `onOrderPaid` - Triggered when an order is paid
* `onOrderRefunded` - Triggered when an order is refunded
* `onRefundCreated` - Triggered when a refund is created
* `onRefundUpdated` - Triggered when a refund is updated
* `onSubscriptionCreated` - Triggered when a subscription is created
* `onSubscriptionUpdated` - Triggered when a subscription is updated
* `onSubscriptionActive` - Triggered when a subscription becomes active
* `onSubscriptionCanceled` - Triggered when a subscription is canceled
* `onSubscriptionRevoked` - Triggered when a subscription is revoked
* `onSubscriptionUncanceled` - Triggered when a subscription cancellation is reversed
* `onProductCreated` - Triggered when a product is created
* `onProductUpdated` - Triggered when a product is updated
* `onOrganizationUpdated` - Triggered when an organization is updated
* `onBenefitCreated` - Triggered when a benefit is created
* `onBenefitUpdated` - Triggered when a benefit is updated
* `onBenefitGrantCreated` - Triggered when a benefit grant is created
* `onBenefitGrantUpdated` - Triggered when a benefit grant is updated
* `onBenefitGrantRevoked` - Triggered when a benefit grant is revoked
* `onCustomerCreated` - Triggered when a customer is created
* `onCustomerUpdated` - Triggered when a customer is updated
* `onCustomerDeleted` - Triggered when a customer is deleted
* `onCustomerStateChanged` - Triggered when a customer state changes


# BetterAuth
Source: https://docs.polar.sh/integrate/sdk/adapters/better-auth

Payments and Checkouts made dead simple with BetterAuth

# @polar-sh/better-auth

A [Better Auth](https://github.com/better-auth/better-auth) plugin for integrating [Polar](https://polar.sh) payments and subscriptions into your authentication flow.

## Features

* [Automatic Customer creation on signup](#automatic-customer-creation-on-signup)
* [Reference System to associate purchases with organizations](#3-2-orders)
* [Checkout Integration](#checkout-plugin)
* [Event Ingestion & Customer Meters for flexible Usage Based Billing](#usage-plugin)
* [Handle Polar Webhooks securely with signature verification](#webhooks-plugin)
* [Customer Portal](#portal-plugin)

## Installation

<Tabs>
  <Tab title="npm">
    Install the BetterAuth and Polar required libraries using the following
    command:

    ```bash
    npm install better-auth @polar-sh/better-auth @polar-sh/sdk
    ```
  </Tab>

  <Tab title="yarn">
    Install the BetterAuth and Polar required libraries using the following
    command:

    ```bash
    yarn add better-auth @polar-sh/better-auth @polar-sh/sdk
    ```
  </Tab>

  <Tab title="pnpm">
    Install the BetterAuth and Polar required libraries using the following
    command:

    ```bash
    pnpm add better-auth @polar-sh/better-auth @polar-sh/sdk
    ```
  </Tab>
</Tabs>

## Integrate Polar with BetterAuth

<Steps>
  <Step title="Configure Polar Access Token">
    Go to your Polar Organization Settings, create an Organization Access Token, and add it to the environment variables of your application.

    ```bash .env
    POLAR_ACCESS_TOKEN=...
    ```
  </Step>

  <Step title="Configure BetterAuth Server">
    The Polar plugin comes with it's own set of plugins to add functionality to your stack:

    * **checkout** - Enable seamless checkout integration
    * **portal** - Make it possible for your customers to manage their orders, subscriptions & benefits
    * **usage** - List customer meters & ingest events for Usage Based Billing
    * **webhooks** - Listen for relevant Polar webhooks

    ```typescript icon="square-js" BetterAuth Server with Polar Example
    import { betterAuth } from "better-auth";
    import { polar, checkout, portal, usage, webhooks } from "@polar-sh/better-auth"; // [!code ++]
    import { Polar } from "@polar-sh/sdk"; // [!code ++]

    const polarClient = new Polar({ // [!code ++]
        accessToken: process.env.POLAR_ACCESS_TOKEN, // [!code ++]
        // Use 'sandbox' if you're using the Polar Sandbox environment
        // Remember that access tokens, products, etc. are completely separated between environments.
        // Access tokens obtained in Production are for instance not usable in the Sandbox environment.
        server: 'sandbox' // [!code ++]
    }); // [!code ++]

    const auth = betterAuth({
        // ... Better Auth config
        plugins: [
            polar({ // [!code ++]
                client: polarClient, // [!code ++]
                createCustomerOnSignUp: true, // [!code ++]
                use: [ // [!code ++]
                    checkout({ // [!code ++]
                        products: [ // [!code ++]
                            { // [!code ++]
                                productId: "123-456-789", // ID of Product from Polar Dashboard // [!code ++]
                                slug: "pro" // Custom slug for easy reference in Checkout URL, e.g. /checkout/pro // [!code ++]
                            } // [!code ++]
                        ], // [!code ++]
                        successUrl: "/success?checkout_id={CHECKOUT_ID}", // [!code ++]
                        authenticatedUsersOnly: true // [!code ++]
                    }), // [!code ++]
                    portal(), // [!code ++]
                    usage(), // [!code ++]
                    webhooks({ // [!code ++]
                        secret: process.env.POLAR_WEBHOOK_SECRET, // [!code ++]
                        onCustomerStateChanged: (payload) => // Triggered when anything regarding a customer changes // [!code ++]
                        onOrderPaid: (payload) => // Triggered when an order was paid (purchase, subscription renewal, etc.) // [!code ++]
                        ...  // Over 25 granular webhook handlers // [!code ++]
                        onPayload: (payload) => // Catch-all for all events // [!code ++]
                    }) // [!code ++]
                ], // [!code ++]
            }) // [!code ++]
        ]
    });
    ```

    #### Polar Plugin Configuration Options

    ```typescript
    // ...

    const auth = betterAuth({
      // ... Better Auth config
      plugins: [
        polar({
          client: polarClient, // [!code ++]
          createCustomerOnSignUp: true, // [!code ++]
          getCustomerCreateParams: ({ user }, request) => ({ // [!code ++]
            metadata: { // [!code ++]
              myCustomProperty: 123, // [!code ++]
            }, // [!code ++]
          }), // [!code ++]
          use: [ // [!code ++]
            // This is where you add Polar plugins // [!code ++]
          ], // [!code ++]
        }),
      ],
    });
    ```

    * `client` (required): Polar SDK client instance
    * `createCustomerOnSignUp` (optional): Automatically create a Polar customer when a user signs up
    * `getCustomerCreateParams` (optional): Custom function to provide additional customer creation metadata
    * `use` (optional): Array of Polar plugins to enable specific functionality (checkout, portal, usage, and webhooks)
  </Step>

  <Step title="Configure BetterAuth Client">
    You will be using the BetterAuth Client to interact with the Polar functionalities.

    ```typescript icon="square-js" BetterAuth Client with Polar Example
    import { createAuthClient } from "better-auth/react";
    import { polarClient } from "@polar-sh/better-auth"; // [!code ++]
    import { organizationClient } from "better-auth/client/plugins"; // [!code ++]

    // All Polar plugins, etc. should be attached to BetterAuth server
    export const authClient = createAuthClient({ // [!code ++]
      plugins: [polarClient()], // [!code ++]
    }); // [!code ++]
    ```
  </Step>
</Steps>

## Automatic Customer creation on signup

Enable the `createCustomerOnSignUp` [Polar plugin configuration option](#polar-plugin-configuration-options) to automatically create a new Polar Customer when a new User is added in the BetterAuth database.

All new customers are created with an associated `externalId`, i.e. the ID of your User in the Database. This skips any Polar to User mapping in your database.

## Checkout Plugin

[Source code](https://github.com/polarsource/polar-adapters/blob/main/packages/polar-betterauth/src/plugins/checkout.ts)

To support [checkouts](/features/checkout/links) in your app, you would pass the `checkout` plugin in the `use` property.

The checkout plugin accepts the following configuration options:

* **`products`** (optional): An array of product mappings or a function that returns them asynchronously. Each mapping contains a `productId` and a `slug` that allows you to reference products by a friendly slug instead of their full ID.
* **`successUrl`** (optional): The relative URL where customers will be redirected after a successful checkout completion. You can use the `{CHECKOUT_ID}` placeholder in the URL to include the checkout session ID in the redirect.
* **`authenticatedUsersOnly`** (optional): A boolean flag that controls whether checkout sessions require user authentication. When set to `true`, only authenticated users can initiate checkouts and the customer information will be automatically associated with the authenticated user. When `false`, anonymous checkouts are allowed.
* **`theme`** (optional): A string that can be used to enforce the theme of the checkout page. Can be either `light` or `dark`.

<Steps>
  <Step title="Use Checkout Plugin">
    Update the `use` property of the Polar plugin for BetterAuth client to have the `checkout` plugin.

    ```typescript icon="square-js" Checkout Plugin Example
    import {
      polar,
      checkout // [!code ++]
    } from "@polar-sh/better-auth";

    const auth = betterAuth({
        // ... Better Auth config
        plugins: [
            polar({
                ...
                use: [
                    checkout({ // [!code ++]
                        // Optional field - will make it possible to pass a slug to checkout instead of Product ID
                        products: [ { productId: "123-456-789", slug: "pro" } ], // [!code ++]
                        // Relative URL to return to when checkout is successfully completed
                        successUrl: "/success?checkout_id={CHECKOUT_ID}", // [!code ++]
                        // Wheather you want to allow unauthenticated checkout sessions or not
                        authenticatedUsersOnly: true // [!code ++]
                    }) // [!code ++]
                ],
            })
        ]
    });
    ```
  </Step>

  <Step title="Create checkouts using BetterAuth client">
    When the `checkout` plugin is passed, you are then able to initialize Checkout Sessions using the `checkout` method on the BetterAuth client. This will redirect the user to the product's checkout link.

    The `checkout` method accepts the following properties:

    * **`products`** (optional): An array of Polar Product IDs.
    * **`slug`** (optional): A string that can be used as a reference to the `products` defined in the Checkout config
    * **`referenceId`** (optional): An identifier that will be saved in the metadata of the checkout, order & subscription object

    ```typescript icon="square-js" BetterAuth Checkout with Polar Example
    await authClient.checkout({
      // Polar Product IDs
      products: ["e651f46d-ac20-4f26-b769-ad088b123df2"], // [!code ++]
      // OR
      // if "products" in passed in the checkout plugin's config, you may pass the slug
      // slug: "pro", // [!code ++]
    });
    ```

    This plugin supports the Organization plugin. If you pass the organization ID to the Checkout referenceId, you will be able to keep track of purchases made from organization members.

    ```typescript icon="square-js" BetterAuth Checkout with Polar Organization Example
    const organizationId = (await authClient.organization.list())?.data?.[0]?.id,

    await authClient.checkout({
        // Any Polar Product ID can be passed here
        products: ["e651f46d-ac20-4f26-b769-ad088b123df2"],
        // Or, if you setup "products" in the Checkout Config, you can pass the slug
        slug: 'pro',
        // Reference ID will be saved as `referenceId` in the metadata of the checkout, order & subscription object
        referenceId: organizationId
    });
    ```
  </Step>
</Steps>

## Usage Plugin

[Source code](https://github.com/polarsource/polar-adapters/blob/main/packages/polar-betterauth/src/plugins/usage.ts)

A plugin for Usage Based Billing that allows you to [ingest events](#event-ingestion) from your application and list the [authenticated user's Usage Meter](#customer-meters).

To enable [usage based billing](/integrate/sdk/adapters/better-auth) in your app, you would pass the `usage` plugin in the `use` property.

```typescript icon="square-js" Usage Plugin Example
import {
  polar, checkout, portal,
  usage // [!code ++]
} from "@polar-sh/better-auth";

const auth = betterAuth({
    // ... Better Auth config
    plugins: [
        polar({
            ...
            use: [
                checkout(...),
                portal(),
                usage() // [!code ++]
            ],
        })
    ]
});
```

### 1. Event Ingestion

Polar's Usage Based Billing builds entirely on event ingestion. Ingest events from your application, create Meters to represent that usage, and add metered prices to Products to charge for it.

The `ingestion` method of the `usage` plugin accepts the following parameters:

* `event` (string): The name of the event to ingest. For example, `ai_usage`, `video_streamed` or `file_uploaded`.

* `metadata` (object): A record containing key-value pairs that provide additional context about the event. Values can be strings, numbers, or booleans. This is useful for storing information that can be used to filter the events or compute the actual usage. For example, you can store the duration of the video streamed or the size of the file uploaded.

<Info>
  The authenticated user is automatically associated with the ingested event.
</Info>

```typescript icon="square-js" Event Ingestion with Usage Plugin Example
const { data: ingested } = await authClient.usage.ingestion({
  event: "file-uploads",
  metadata: {
    uploadedFiles: 12,
  },
});
```

### 2. Customer Meters

A method to list the authenticated user's Usage Meters (aka Customer Meters). A Customer Meter contains all the information about their consumption on your defined meters.

The `meters` method of the `usage` plugin accepts the following parameters:

* `page` (number): The page number for pagination (starts from 1).

* `limit` (number): The maximum number of meters to return per page.

```typescript icon="square-js" Customer Meters with Usage Plugin Example
const { data: customerMeters } = await authClient.usage.meters.list({
  query: {
    page: 1,
    limit: 10,
  },
});
```

The `meters` method returns the following fields in the response object:

* **Customer Information**: Details about the authenticated customer
* **Meter Information**: Configuration and settings of the usage meter
* **Customer Meter Information**:
  * **Consumed Units**: Total units consumed by the customer
  * **Credited Units**: Total units credited to the customer
  * **Balance**: The balance of the meter, i.e. the difference between credited and consumed units.

## Webhooks Plugin

[Source code](https://github.com/polarsource/polar-adapters/blob/main/packages/polar-betterauth/src/plugins/webhooks.ts)

The `webhooks` plugin can be used to capture incoming events from your Polar organization.

To set up the Polar `webhooks` plugin with the BetterAuth client, follow the steps below:

<Steps>
  <Step title="Configure Webhook Endpoints in Polar">
    Configure a Webhook endpoint in your Polar Organization Settings page by following [this guide](/integrate/webhooks/endpoints). Webhook endpoint is configured at `/api/auth/polar/webhooks`.
  </Step>

  <Step title="Add the Webhook Secret">
    Add the obtained webhook secret to your application environment as an environment variable (to be used as `process.env.POLAR_WEBHOOK_SECRET`):

    ```bash .env
    POLAR_WEBHOOK_SECRET="..."
    ```
  </Step>

  <Step title="Use Webhooks Plugin in BetterAuth client">
    Pass the `webhooks` plugin in the `use` property.

    ```typescript icon="square-js" Webhooks Plugin Example
    import {
      polar,
      webhooks // [!code ++]
    } from "@polar-sh/better-auth";

    const auth = betterAuth({
        // ... Better Auth config
        plugins: [
            polar({
                ...
                use: [
                    webhooks({ // [!code ++]
                        secret: process.env.POLAR_WEBHOOK_SECRET, // [!code ++]
                        onCustomerStateChanged: (payload) => // Triggered when anything regarding a customer changes // [!code ++]
                        onOrderPaid: (payload) => // Triggered when an order was paid (purchase, subscription renewal, etc.) // [!code ++]
                        ...  // Over 25 granular webhook handlers // [!code ++]
                        onPayload: (payload) => // Catch-all for all events // [!code ++]
                    }) // [!code ++]
                ],
            })
        ]
    });
    ```
  </Step>
</Steps>

The `webhooks` plugin allows you to invoke handlers for all Polar webhook events:

* `onPayload` - Catch-all handler for any incoming Webhook event
* `onCheckoutCreated` - Triggered when a checkout is created
* `onCheckoutUpdated` - Triggered when a checkout is updated
* `onOrderCreated` - Triggered when an order is created
* `onOrderPaid` - Triggered when an order is paid
* `onOrderRefunded` - Triggered when an order is refunded
* `onRefundCreated` - Triggered when a refund is created
* `onRefundUpdated` - Triggered when a refund is updated
* `onSubscriptionCreated` - Triggered when a subscription is created
* `onSubscriptionUpdated` - Triggered when a subscription is updated
* `onSubscriptionActive` - Triggered when a subscription becomes active
* `onSubscriptionCanceled` - Triggered when a subscription is canceled
* `onSubscriptionRevoked` - Triggered when a subscription is revoked
* `onSubscriptionUncanceled` - Triggered when a subscription cancellation is reversed
* `onProductCreated` - Triggered when a product is created
* `onProductUpdated` - Triggered when a product is updated
* `onOrganizationUpdated` - Triggered when an organization is updated
* `onBenefitCreated` - Triggered when a benefit is created
* `onBenefitUpdated` - Triggered when a benefit is updated
* `onBenefitGrantCreated` - Triggered when a benefit grant is created
* `onBenefitGrantUpdated` - Triggered when a benefit grant is updated
* `onBenefitGrantRevoked` - Triggered when a benefit grant is revoked
* `onCustomerCreated` - Triggered when a customer is created
* `onCustomerUpdated` - Triggered when a customer is updated
* `onCustomerDeleted` - Triggered when a customer is deleted
* `onCustomerStateChanged` - Triggered when a customer state changes

## Portal Plugin

[Source code](https://github.com/polarsource/polar-adapters/blob/main/packages/polar-betterauth/src/plugins/portal.ts)

A plugin which enables customer management of their purchases, orders and subscriptions.

```typescript icon="square-js" Portal Plugin Example
import {
  polar, checkout,
  portal // [!code ++]
} from "@polar-sh/better-auth";

const auth = betterAuth({
    // ... Better Auth config
    plugins: [
        polar({
            ...
            use: [
                checkout(...),
                portal() // [!code ++]
            ],
        })
    ]
});
```

The `portal` plugin gives the BetterAuth Client a set of customer management methods, scoped under `authClient.customer` object.

### 1. Customer Portal Management

The following method will redirect the user to the Polar Customer Portal, where they can see their orders, purchases, subscriptions, benefits, etc.

```typescript icon="square-js" Open Customer Portal Example
await authClient.customer.portal();
```

### 2. Customer State

The portal plugin also adds a convenient method to retrieve the Customer State.

```typescript icon="square-js" Retrieve Customer State Example
const { data: customerState } = await authClient.customer.state();
```

The customer state object contains:

* All the data about the customer.
* The list of their active subscriptions.
  <Note>
    This does not include subscriptions done by a parent organization. See the
    subscription list-method below for more information.
  </Note>
* The list of their granted benefits.
* The list of their active meters, with their current balance.

Using the customer state object, you can determine whether to provision access for the user to your service.

Learn more about the Polar Customer State [in the Polar Docs](https://docs.polar.sh/integrate/customer-state).

### 3. Benefits, Orders & Subscriptions

The portal plugin adds the following 3 convenient methods for listing benefits, orders & subscriptions relevant to the authenticated user/customer.

#### 3.1 Benefits

This method only lists granted benefits for the authenticated user/customer.

```typescript icon="square-js" List User Benefits Example
const { data: benefits } = await authClient.customer.benefits.list({
  query: {
    page: 1,
    limit: 10,
  },
});
```

#### 3.2 Orders

This method lists orders like purchases and subscription renewals for the authenticated user/customer.

```typescript icon="square-js" List User Orders Example
const { data: orders } = await authClient.customer.orders.list({
  query: {
    page: 1,
    limit: 10,
    productBillingType: "one_time", // or 'recurring'
  },
});
```

Using the Organization ID as the `referenceId` you can retrieve all the subscriptions associated with that organization (instead of the user).

To figure out if a user should have access, pass the user's organization ID to see if there is an active subscription for that organization.

```typescript icon="square-js" List Organization Subscriptions Example
const organizationId = (await authClient.organization.list())?.data?.[0]?.id,

const { data: subscriptions } = await authClient.customer.orders.list({
    query: {
	    page: 1,
		limit: 10,
		active: true,
        referenceId: organizationId
    },
});

const userShouldHaveAccess = subscriptions.some(
    sub => // Your logic to check subscription product or whatever.
)
```

#### 3.3 Subscriptions

This method lists the subscriptions associated with authenticated user/customer.

```typescript icon="square-js" List User Subscriptions Example
const { data: subscriptions } = await authClient.customer.subscriptions.list({
  query: {
    page: 1,
    limit: 10,
    active: true,
  },
});
```

<Danger>
  This will not return subscriptions made by a parent organization to the
  authenticated user.
</Danger>


# Deno
Source: https://docs.polar.sh/integrate/sdk/adapters/deno

Payments and Checkouts made dead simple with Deno

## Checkout

Create a Checkout handler which takes care of redirections.

```typescript
import { Checkout } from "jsr:@polar-sh/deno";

Deno.serve(
  Checkout({
    accessToken: "xxx",
    theme: "dark", // Enforces the theme - System-preferred theme will be set if left omitted
  })
);
```

### Query Params

Pass query params to this route.

* products `?products=123`
* customerId (optional) `?products=123&customerId=xxx`
* customerExternalId (optional) `?products=123&customerExternalId=xxx`
* customerEmail (optional) `?products=123&customerEmail=<EMAIL>`
* customerName (optional) `?products=123&customerName=Jane`
* metadata (optional) `URL-Encoded JSON string`

## Customer Portal

Create a customer portal where your customer can view orders and subscriptions.

```typescript
import { CustomerPortal } from "jsr:@polar-sh/deno";

Deno.serve(
  CustomerPortal({
    accessToken: "xxx",
    getCustomerId: (req) => "",
    server: "sandbox",
  })
);
```

## Webhooks

A simple utility which resolves incoming webhook payloads by signing the webhook secret properly.

```typescript
import { Webhooks } from "jsr:@polar-sh/deno";

Deno.serve(
    Webhooks({
        webhookSecret: Deno.env.get('POLAR_WEBHOOK_SECRET'),
        onPayload: async (payload) => /** Handle payload */,
    })
);
```

### Payload Handlers

The Webhook handler also supports granular handlers for easy integration.

* `onPayload` - Catch-all handler for any incoming Webhook event
* `onCheckoutCreated` - Triggered when a checkout is created
* `onCheckoutUpdated` - Triggered when a checkout is updated
* `onOrderCreated` - Triggered when an order is created
* `onOrderPaid` - Triggered when an order is paid
* `onOrderRefunded` - Triggered when an order is refunded
* `onRefundCreated` - Triggered when a refund is created
* `onRefundUpdated` - Triggered when a refund is updated
* `onSubscriptionCreated` - Triggered when a subscription is created
* `onSubscriptionUpdated` - Triggered when a subscription is updated
* `onSubscriptionActive` - Triggered when a subscription becomes active
* `onSubscriptionCanceled` - Triggered when a subscription is canceled
* `onSubscriptionRevoked` - Triggered when a subscription is revoked
* `onSubscriptionUncanceled` - Triggered when a subscription cancellation is reversed
* `onProductCreated` - Triggered when a product is created
* `onProductUpdated` - Triggered when a product is updated
* `onOrganizationUpdated` - Triggered when an organization is updated
* `onBenefitCreated` - Triggered when a benefit is created
* `onBenefitUpdated` - Triggered when a benefit is updated
* `onBenefitGrantCreated` - Triggered when a benefit grant is created
* `onBenefitGrantUpdated` - Triggered when a benefit grant is updated
* `onBenefitGrantRevoked` - Triggered when a benefit grant is revoked
* `onCustomerCreated` - Triggered when a customer is created
* `onCustomerUpdated` - Triggered when a customer is updated
* `onCustomerDeleted` - Triggered when a customer is deleted
* `onCustomerStateChanged` - Triggered when a customer state changes


# Elysia
Source: https://docs.polar.sh/integrate/sdk/adapters/elysia

Payments and Checkouts made dead simple with Elysia

```bash
pnpm install @polar-sh/elysia zod
```

### Checkout

Create a Checkout handler which takes care of redirections.

```typescript
import { Elysia } from "elysia";
import { Checkout } from "@polar-sh/elysia";

const app = new Elysia();

app.get(
  "/checkout",
  Checkout({
    accessToken: "xxx", // Or set an environment variable to POLAR_ACCESS_TOKEN
    successUrl: process.env.SUCCESS_URL,
    server: "sandbox", // Use sandbox if you're testing Polar - omit the parameter or pass 'production' otherwise
    theme: "dark", // Enforces the theme - System-preferred theme will be set if left omitted
  })
);
```

#### Query Params

Pass query params to this route.

* products `?products=123`
* customerId (optional) `?products=123&customerId=xxx`
* customerExternalId (optional) `?products=123&customerExternalId=xxx`
* customerEmail (optional) `?products=123&customerEmail=<EMAIL>`
* customerName (optional) `?products=123&customerName=Jane`
* metadata (optional) `URL-Encoded JSON string`

### Customer Portal

Create a customer portal where your customer can view orders and subscriptions.

```typescript
import { Elysia } from "elysia";
import { CustomerPortal } from "@polar-sh/elysia";

const app = new Elysia();

app.get(
  "/portal",
  CustomerPortal({
    accessToken: "xxx", // Or set an environment variable to POLAR_ACCESS_TOKEN
    getCustomerId: (event) => "", // Function to resolve a Polar Customer ID
    server: "sandbox", // Use sandbox if you're testing Polar - omit the parameter or pass 'production' otherwise
  })
);
```

### Webhooks

A simple utility which resolves incoming webhook payloads by signing the webhook secret properly.

```typescript
import { Elysia } from 'elysia'
import { Webhooks } from "@polar-sh/elysia";

const app = new Elysia()

app.post('/polar/webhooks', Webhooks({
  webhookSecret: process.env.POLAR_WEBHOOK_SECRET!,
  onPayload: async (payload) => /** Handle payload */,
}))
```

### Payload Handlers

The Webhook handler also supports granular handlers for easy integration.

* `onPayload` - Catch-all handler for any incoming Webhook event
* `onCheckoutCreated` - Triggered when a checkout is created
* `onCheckoutUpdated` - Triggered when a checkout is updated
* `onOrderCreated` - Triggered when an order is created
* `onOrderPaid` - Triggered when an order is paid
* `onOrderRefunded` - Triggered when an order is refunded
* `onRefundCreated` - Triggered when a refund is created
* `onRefundUpdated` - Triggered when a refund is updated
* `onSubscriptionCreated` - Triggered when a subscription is created
* `onSubscriptionUpdated` - Triggered when a subscription is updated
* `onSubscriptionActive` - Triggered when a subscription becomes active
* `onSubscriptionCanceled` - Triggered when a subscription is canceled
* `onSubscriptionRevoked` - Triggered when a subscription is revoked
* `onSubscriptionUncanceled` - Triggered when a subscription cancellation is reversed
* `onProductCreated` - Triggered when a product is created
* `onProductUpdated` - Triggered when a product is updated
* `onOrganizationUpdated` - Triggered when an organization is updated
* `onBenefitCreated` - Triggered when a benefit is created
* `onBenefitUpdated` - Triggered when a benefit is updated
* `onBenefitGrantCreated` - Triggered when a benefit grant is created
* `onBenefitGrantUpdated` - Triggered when a benefit grant is updated
* `onBenefitGrantRevoked` - Triggered when a benefit grant is revoked
* `onCustomerCreated` - Triggered when a customer is created
* `onCustomerUpdated` - Triggered when a customer is updated
* `onCustomerDeleted` - Triggered when a customer is deleted
* `onCustomerStateChanged` - Triggered when a customer state changes


# Express
Source: https://docs.polar.sh/integrate/sdk/adapters/express

Payments and Checkouts made dead simple with Express

```bash
pnpm install @polar-sh/express zod
```

## Checkout

Create a Checkout handler which takes care of redirections.

```typescript
import express from "express";
import { Checkout } from "@polar-sh/express";

const app = express();

app.get(
  "/checkout",
  Checkout({
    accessToken: "xxx", // Or set an environment variable to POLAR_ACCESS_TOKEN
    successUrl: process.env.SUCCESS_URL,
    server: "sandbox", // Use sandbox if you're testing Polar - omit the parameter or pass 'production' otherwise
    theme: "dark", // Enforces the theme - System-preferred theme will be set if left omitted
  })
);
```

### Query Params

Pass query params to this route.

* products `?products=123`
* customerId (optional) `?products=123&customerId=xxx`
* customerExternalId (optional) `?products=123&customerExternalId=xxx`
* customerEmail (optional) `?products=123&customerEmail=<EMAIL>`
* customerName (optional) `?products=123&customerName=Jane`
* metadata (optional) `URL-Encoded JSON string`

## Customer Portal

Create a customer portal where your customer can view orders and subscriptions.

```typescript
import express from "express";
import { CustomerPortal } from "@polar-sh/express";

const app = express();

app.get(
  "/portal",
  CustomerPortal({
    accessToken: "xxx", // Or set an environment variable to POLAR_ACCESS_TOKEN
    getCustomerId: (event) => "", // Function to resolve a Polar Customer ID
    server: "sandbox", // Use sandbox if you're testing Polar - omit the parameter or pass 'production' otherwise
  })
);
```

## Webhooks

A simple utility which resolves incoming webhook payloads by signing the webhook secret properly.

```typescript
import express from 'express'
import { Webhooks } from "@polar-sh/express";

const app = express()

app
.use(express.json())
.post('/polar/webhooks', Webhooks({
  webhookSecret: process.env.POLAR_WEBHOOK_SECRET!,
  onPayload: async (payload) => /** Handle payload */,
}))
```

### Payload Handlers

The Webhook handler also supports granular handlers for easy integration.

* `onPayload` - Catch-all handler for any incoming Webhook event
* `onCheckoutCreated` - Triggered when a checkout is created
* `onCheckoutUpdated` - Triggered when a checkout is updated
* `onOrderCreated` - Triggered when an order is created
* `onOrderPaid` - Triggered when an order is paid
* `onOrderRefunded` - Triggered when an order is refunded
* `onRefundCreated` - Triggered when a refund is created
* `onRefundUpdated` - Triggered when a refund is updated
* `onSubscriptionCreated` - Triggered when a subscription is created
* `onSubscriptionUpdated` - Triggered when a subscription is updated
* `onSubscriptionActive` - Triggered when a subscription becomes active
* `onSubscriptionCanceled` - Triggered when a subscription is canceled
* `onSubscriptionRevoked` - Triggered when a subscription is revoked
* `onSubscriptionUncanceled` - Triggered when a subscription cancellation is reversed
* `onProductCreated` - Triggered when a product is created
* `onProductUpdated` - Triggered when a product is updated
* `onOrganizationUpdated` - Triggered when an organization is updated
* `onBenefitCreated` - Triggered when a benefit is created
* `onBenefitUpdated` - Triggered when a benefit is updated
* `onBenefitGrantCreated` - Triggered when a benefit grant is created
* `onBenefitGrantUpdated` - Triggered when a benefit grant is updated
* `onBenefitGrantRevoked` - Triggered when a benefit grant is revoked
* `onCustomerCreated` - Triggered when a customer is created
* `onCustomerUpdated` - Triggered when a customer is updated
* `onCustomerDeleted` - Triggered when a customer is deleted
* `onCustomerStateChanged` - Triggered when a customer state changes


# Fastify
Source: https://docs.polar.sh/integrate/sdk/adapters/fastify

Payments and Checkouts made dead simple with Fastify

```bash
pnpm install @polar-sh/fastify zod
```

## Checkout

Create a Checkout handler which takes care of redirections.

```typescript
import fastify from "fastify";
import { Checkout } from "@polar-sh/fastify";

fastify().get(
  "/checkout",
  Checkout({
    accessToken: "xxx", // Or set an environment variable to POLAR_ACCESS_TOKEN
    successUrl: process.env.SUCCESS_URL,
    server: "sandbox", // Use sandbox if you're testing Polar - omit the parameter or pass 'production' otherwise
    theme: "dark", // Enforces the theme - System-preferred theme will be set if left omitted
  })
);
```

### Query Params

Pass query params to this route.

* products `?products=123`
* customerId (optional) `?products=123&customerId=xxx`
* customerExternalId (optional) `?products=123&customerExternalId=xxx`
* customerEmail (optional) `?products=123&customerEmail=<EMAIL>`
* customerName (optional) `?products=123&customerName=Jane`
* metadata (optional) `URL-Encoded JSON string`

## Customer Portal

Create a customer portal where your customer can view orders and subscriptions.

```typescript
import fastify from "fastify";
import { CustomerPortal } from "@polar-sh/fastify";

fastify().get(
  "/portal",
  CustomerPortal({
    accessToken: "xxx", // Or set an environment variable to POLAR_ACCESS_TOKEN
    getCustomerId: (event) => "", // Function to resolve a Polar Customer ID
    server: "sandbox", // Use sandbox if you're testing Polar - omit the parameter or pass 'production' otherwise
  })
);
```

## Webhooks

A simple utility which resolves incoming webhook payloads by signing the webhook secret properly.

```typescript
import fastify from 'fastify'
import { Webhooks } from "@polar-sh/fastify";

fastify.post('/polar/webhooks', Webhooks({
  webhookSecret: process.env.POLAR_WEBHOOK_SECRET!,
  onPayload: async (payload) => /** Handle payload */,
}))
```

### Payload Handlers

The Webhook handler also supports granular handlers for easy integration.

* `onPayload` - Catch-all handler for any incoming Webhook event
* `onCheckoutCreated` - Triggered when a checkout is created
* `onCheckoutUpdated` - Triggered when a checkout is updated
* `onOrderCreated` - Triggered when an order is created
* `onOrderPaid` - Triggered when an order is paid
* `onOrderRefunded` - Triggered when an order is refunded
* `onRefundCreated` - Triggered when a refund is created
* `onRefundUpdated` - Triggered when a refund is updated
* `onSubscriptionCreated` - Triggered when a subscription is created
* `onSubscriptionUpdated` - Triggered when a subscription is updated
* `onSubscriptionActive` - Triggered when a subscription becomes active
* `onSubscriptionCanceled` - Triggered when a subscription is canceled
* `onSubscriptionRevoked` - Triggered when a subscription is revoked
* `onSubscriptionUncanceled` - Triggered when a subscription cancellation is reversed
* `onProductCreated` - Triggered when a product is created
* `onProductUpdated` - Triggered when a product is updated
* `onOrganizationUpdated` - Triggered when an organization is updated
* `onBenefitCreated` - Triggered when a benefit is created
* `onBenefitUpdated` - Triggered when a benefit is updated
* `onBenefitGrantCreated` - Triggered when a benefit grant is created
* `onBenefitGrantUpdated` - Triggered when a benefit grant is updated
* `onBenefitGrantRevoked` - Triggered when a benefit grant is revoked
* `onCustomerCreated` - Triggered when a customer is created
* `onCustomerUpdated` - Triggered when a customer is updated
* `onCustomerDeleted` - Triggered when a customer is deleted
* `onCustomerStateChanged` - Triggered when a customer state changes


# Hono
Source: https://docs.polar.sh/integrate/sdk/adapters/hono

Payments and Checkouts made dead simple with Hono

```bash
pnpm install @polar-sh/hono zod
```

## Checkout

Create a Checkout handler which takes care of redirections.

```typescript
import { Hono } from "hono";
import { Checkout } from "@polar-sh/hono";

const app = new Hono();

app.get(
  "/checkout",
  Checkout({
    accessToken: "xxx", // Or set an environment variable to POLAR_ACCESS_TOKEN
    successUrl: process.env.SUCCESS_URL,
    server: "sandbox", // Use sandbox if you're testing Polar - omit the parameter or pass 'production' otherwise
    theme: "dark", // Enforces the theme - System-preferred theme will be set if left omitted
  })
);
```

### Query Params

Pass query params to this route.

* products `?products=123`
* customerId (optional) `?products=123&customerId=xxx`
* customerExternalId (optional) `?products=123&customerExternalId=xxx`
* customerEmail (optional) `?products=123&customerEmail=<EMAIL>`
* customerName (optional) `?products=123&customerName=Jane`
* metadata (optional) `URL-Encoded JSON string`

## Customer Portal

Create a customer portal where your customer can view orders and subscriptions.

```typescript
import { Hono } from "hono";
import { CustomerPortal } from "@polar-sh/hono";

const app = new Hono();

app.get(
  "/portal",
  CustomerPortal({
    accessToken: "xxx", // Or set an environment variable to POLAR_ACCESS_TOKEN
    getCustomerId: (event) => "", // Function to resolve a Polar Customer ID
    server: "sandbox", // Use sandbox if you're testing Polar - omit the parameter or pass 'production' otherwise
  })
);
```

## Webhooks

A simple utility which resolves incoming webhook payloads by signing the webhook secret properly.

```typescript
import { Hono } from 'hono'
import { Webhooks } from "@polar-sh/hono";

const app = new Hono()

app.post('/polar/webhooks', Webhooks({
  webhookSecret: process.env.POLAR_WEBHOOK_SECRET!,
  onPayload: async (payload) => /** Handle payload */,
}))
```

### Payload Handlers

The Webhook handler also supports granular handlers for easy integration.

* `onPayload` - Catch-all handler for any incoming Webhook event
* `onCheckoutCreated` - Triggered when a checkout is created
* `onCheckoutUpdated` - Triggered when a checkout is updated
* `onOrderCreated` - Triggered when an order is created
* `onOrderPaid` - Triggered when an order is paid
* `onOrderRefunded` - Triggered when an order is refunded
* `onRefundCreated` - Triggered when a refund is created
* `onRefundUpdated` - Triggered when a refund is updated
* `onSubscriptionCreated` - Triggered when a subscription is created
* `onSubscriptionUpdated` - Triggered when a subscription is updated
* `onSubscriptionActive` - Triggered when a subscription becomes active
* `onSubscriptionCanceled` - Triggered when a subscription is canceled
* `onSubscriptionRevoked` - Triggered when a subscription is revoked
* `onSubscriptionUncanceled` - Triggered when a subscription cancellation is reversed
* `onProductCreated` - Triggered when a product is created
* `onProductUpdated` - Triggered when a product is updated
* `onOrganizationUpdated` - Triggered when an organization is updated
* `onBenefitCreated` - Triggered when a benefit is created
* `onBenefitUpdated` - Triggered when a benefit is updated
* `onBenefitGrantCreated` - Triggered when a benefit grant is created
* `onBenefitGrantUpdated` - Triggered when a benefit grant is updated
* `onBenefitGrantRevoked` - Triggered when a benefit grant is revoked
* `onCustomerCreated` - Triggered when a customer is created
* `onCustomerUpdated` - Triggered when a customer is updated
* `onCustomerDeleted` - Triggered when a customer is deleted
* `onCustomerStateChanged` - Triggered when a customer state changes


# null
Source: https://docs.polar.sh/integrate/sdk/adapters/laravel



![](https://banners.beyondco.de/laravel-polar.png?theme=dark\&packageManager=composer+require\&packageName=danestves%2Flaravel-polar\&pattern=pieFactory\&style=style_1\&description=Easily+integrate+your+Laravel+application+with+Polar.sh\&md=1\&showWatermark=1\&fontSize=100px\&images=https%3A%2F%2Flaravel.com%2Fimg%2Flogomark.min.svg "Laravel Polar")

# Polar for Laravel

Seamlessly integrate Polar.sh subscriptions and payments into your Laravel application. This package provides an elegant way to handle subscriptions, manage recurring payments, and interact with Polar's API. With built-in support for webhooks, subscription management, and a fluent API, you can focus on building your application while we handle the complexities of subscription billing.

## Installation

**Step 1:** You can install the package via composer:

```bash
composer require danestves/laravel-polar
```

**Step 2:** Run `:install`:

```bash
php artisan polar:install
```

This will publish the config, migrations and views, and ask to run the migrations.

Or publish and run the migrations individually:

```bash
php artisan vendor:publish --tag="polar-migrations"
```

```bash
php artisan vendor:publish --tag="polar-config"
```

```bash
php artisan vendor:publish --tag="polar-views"
```

```bash
php artisan migrate
```

This is the contents of the published config file:

```php
<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Polar Access Token
    |--------------------------------------------------------------------------
    |
    | The Polar access token is used to authenticate with the Polar API.
    | You can find your access token in the Polar dashboard > Settings
    | under the "Developers" section.
    |
    */
    'access_token' => env('POLAR_ACCESS_TOKEN'),

    /*
    |--------------------------------------------------------------------------
    | Polar Webhook Secret
    |--------------------------------------------------------------------------
    |
    | The Polar webhook secret is used to verify that the webhook requests
    | are coming from Polar. You can find your webhook secret in the Polar
    | dashboard > Settings > Webhooks on each registered webhook.
    |
    | We (the developers) recommend using a single webhook for all your
    | integrations. This way you can use the same secret for all your
    | integrations and you don't have to manage multiple webhooks.
    |
    */
    'webhook_secret' => env('POLAR_WEBHOOK_SECRET'),

    /*
    |--------------------------------------------------------------------------
    | Polar Url Path
    |--------------------------------------------------------------------------
    |
    | This is the base URI where routes from Polar will be served
    | from. The URL built into Polar is used by default; however,
    | you can modify this path as you see fit for your application.
    |
    */
    'path' => env('POLAR_PATH', 'polar'),

    /*
    |--------------------------------------------------------------------------
    | Default Redirect URL
    |--------------------------------------------------------------------------
    |
    | This is the default redirect URL that will be used when a customer
    | is redirected back to your application after completing a purchase
    | from a checkout session in your Polar account.
    |
    */
    'redirect_url' => null,

    /*
    |--------------------------------------------------------------------------
    | Currency Locale
    |--------------------------------------------------------------------------
    |
    | This is the default locale in which your money values are formatted in
    | for display. To utilize other locales besides the default "en" locale
    | verify you have to have the "intl" PHP extension installed on the system.
    |
    */
    'currency_locale' => env('POLAR_CURRENCY_LOCALE', 'en'),
];
```

## Usage

### Access Token

Configure your access token. Create a new token in the Polar Dashboard > Settings > Developers and paste it in the `.env` file.

* [https://sandbox.polar.sh/dashboard/ORG\_SLUG/settings](https://sandbox.polar.sh/dashboard/ORG_SLUG/settings) (Sandbox)
* [https://polar.sh/dashboard/ORG\_SLUG/settings](https://polar.sh/dashboard/ORG_SLUG/settings) (Production)

```bash
POLAR_ACCESS_TOKEN="<your_access_token>"
```

### Webhook Secret

Configure your webhook secret. Create a new webhook in the Polar Dashboard > Settings > Webhooks.

* [https://sandbox.polar.sh/dashboard/ORG\_SLUG/settings/webhooks](https://sandbox.polar.sh/dashboard/ORG_SLUG/settings/webhooks) (Sandbox)
* [https://polar.sh/dashboard/ORG\_SLUG/settings/webhooks](https://polar.sh/dashboard/ORG_SLUG/settings/webhooks) (Production)

Configure the webhook for the following events that this pacckage supports:

* `order.created`
* `order.updated`
* `subscription.created`
* `subscription.updated`
* `subscription.active`
* `subscription.canceled`
* `subscription.revoked`
* `benefit_grant.created`
* `benefit_grant.updated`
* `benefit_grant.revoked`

```bash
POLAR_WEBHOOK_SECRET="<your_webhook_secret>"
```

### Billable Trait

Let’s make sure everything’s ready for your customers to checkout smoothly. 🛒

First, we’ll need to set up a model to handle billing—don’t worry, it’s super simple! In most cases, this will be your app’s User model. Just add the Billable trait to your model like this (you’ll import it from the package first, of course):

```php
use Danestves\LaravelPolar\Billable;

class User extends Authenticatable
{
    use Billable;
}
```

Now the user model will have access to the methods provided by the package. You can make any model billable by adding the trait to it, not just the User model.

### Polar Script

Polar includes a JavaScript script that you can use to initialize the [Polar Embedded Checkout](https://docs.polar.sh/features/checkout/embed). If you going to use this functionality, you can use the `@polarEmbedScript` directive to include the script in your views inside the `<head>` tag.

```blade
<head>
    ...

    @polarEmbedScript
</head>
```

### Webhooks

This package includes a webhook handler that will handle the webhooks from Polar.

#### Webhooks & CSRF Protection

Incoming webhooks should not be affected by [CSRF protection](https://laravel.com/docs/csrf). To prevent this, add your webhook path to the except list of your `App\Http\Middleware\VerifyCsrfToken` middleware:

```php
protected $except = [
    'polar/*',
];
```

Or if you're using Laravel v11 and up, you should exclude `polar/*` in your application's `bootstrap/app.php` file:

```php
->withMiddleware(function (Middleware $middleware) {
    $middleware->validateCsrfTokens(except: [
        'polar/*',
    ]);
})
```

### Commands

This package includes a list of commands that you can use to retrieve information about your Polar account.

| Command                      | Description                                |
| ---------------------------- | ------------------------------------------ |
| `php artisan polar:products` | List all available products with their ids |

### Checkouts

#### Single Payments

To create a checkout to show only a single payment, pass a single items to the array of products when creating the checkout.

```php
use Illuminate\Http\Request;

Route::get('/subscribe', function (Request $request) {
    return $request->user()->checkout(['product_id_123']);
});
```

If you want to show multiple products that the user can choose from, you can pass an array of product ids to the `checkout` method.

```php
use Illuminate\Http\Request;

Route::get('/subscribe', function (Request $request) {
    return $request->user()->checkout(['product_id_123', 'product_id_456']);
});
```

This could be useful if you want to offer monthly, yearly, and lifetime plans for example.

> \[!NOTE]
> If you are requesting the checkout a lot of times we recommend you to cache the URL returned by the `checkout` method.

#### Custom Price

You can override the price of a product using the `charge` method.

```php
use Illuminate\Http\Request;

Route::get('/subscribe', function (Request $request) {
    return $request->user()->charge(1000, ['product_id_123']);
});
```

#### Embedded Checkout

Instead of redirecting the user you can create the checkout link, pass it to the page and use our blade component:

```php
use Illuminate\Http\Request;

Route::get('/billing', function (Request $request) {
    $checkout = $request->user()->checkout(['product_id_123']);

    return view('billing', ['checkout' => $checkout]);
});
```

Now we can use the button like this:

```blade
<x-polar-button :checkout="$checkout" />
```

The component accepts the normal props that a link element accepts. You can change the theme of the embedded checkout by using the following prop:

```blade
<x-polar-button :checkout="$checkout" data-polar-checkout-theme="dark" />
```

It defaults to light theme, so you only need to pass the prop if you want to change it.

### Prefill Customer Information

You can override the user data using the following methods in your models provided by the `Billable` trait.

```php
public function polarName(): ?string; // default: $model->name
public function polarEmail(): ?string; // default: $model->email
```

### Redirects After Purchase

You can redirect the user to a custom page after the purchase using the `withSuccessUrl` method:

```php
$request->user()->checkout('variant-id')
    ->withSuccessUrl(url('/success'));
```

You can also add the `checkout_id={CHECKOUT_ID}` query parameter to the URL to retrieve the checkout session id:

```php
$request->user()->checkout('variant-id')
    ->withSuccessUrl(url('/success?checkout_id={CHECKOUT_ID}'));
```

### Custom metadata and customer metadata

You can add custom metadata to the checkout session using the `withMetadata` method:

```php
$request->user()->checkout('variant-id')
    ->withMetadata(['key' => 'value']);
```

You can also add customer metadata to the checkout session using the `withCustomerMetadata` method:

```php
$request->user()->checkout('variant-id')
    ->withCustomerMetadata(['key' => 'value']);
```

These will then be available in the relevant webhooks for you.

#### Reserved Keywords

When working with custom data, this library has a few reserved terms.

* `billable_id`
* `billable_type`
* `subscription_type`

Using any of these will result in an exception being thrown.

### Customers

#### Customer Portal

Customers can update their personal information (e.g., name, email address) by accessing their [self-service customer portal](https://docs.polar.sh/features/customer-portal). To redirect customers to this portal, call the `redirectToCustomerPortal()` method on your billable model (e.g., the User model).

```php
use Illuminate\Http\Request;

Route::get('/customer-portal', function (Request $request) {
    return $request->user()->redirectToCustomerPortal();
});
```

Optionally, you can obtain the signed customer portal URL directly:

```php
$url = $user->customerPortalUrl();
```

### Orders

#### Retrieving Orders

You can retrieve orders by using the `orders` relationship on the billable model:

```blade
<table>
    @foreach ($user->orders as $order)
        <td>{{ $order->ordered_at->toFormattedDateString() }}</td>
        <td>{{ $order->polar_id }}</td>
        <td>{{ $order->amount }}</td>
        <td>{{ $order->tax_amount }}</td>
        <td>{{ $order->refunded_amount }}</td>
        <td>{{ $order->refunded_tax_amount }}</td>
        <td>{{ $order->currency }}</td>
        <!-- Add more columns as needed -->
    @endforeach
</table>
```

#### Check order status

You can check the status of an order by using the `status` attribute:

```php
$order->status;
```

Or you can use some of the helper methods offers by the `Order` model:

```php
$order->paid();
```

Aside from that, you can run two other checks: refunded, and partially refunded. If the order is refunded, you can utilize the refunded\_at timestamp:

```blade
@if ($order->refunded())
    Order {{ $order->polar_id }} was refunded on {{ $order->refunded_at->toFormattedDateString() }}
@endif
```

You may also see if an order was for a certain product:

```php
if ($order->hasProduct('product_id_123')) {
    // ...
}
```

Or for an specific price:

```php
if ($order->hasPrice('price_id_123')) {
    // ...
}
```

Furthermore, you can check if a consumer has purchased a specific product:

```php
if ($user->hasPurchasedProduct('product_id_123')) {
    // ...
}
```

Or for an specific price:

```php
if ($user->hasPurchasedPrice('price_id_123')) {
    // ...
}
```

### Subscriptions

#### Creating Subscriptions

Starting a subscription is simple. For this, we require our product's variant id. Copy the product id and start a new subscription checkout using your billable model:

```php
use Illuminate\Http\Request;

Route::get('/subscribe', function (Request $request) {
    return $request->user()->subscribe('product_id_123');
});
```

When a customer completes their checkout, the incoming `SubscriptionCreated` event webhook connects it to your billable model in the database. You may then get the subscription from your billable model:

```php
$subscription = $user->subscription();
```

#### Checking Subscription Status

Once a consumer has subscribed to your services, you can use a variety of methods to check on the status of their subscription. The most basic example is to check if a customer has a valid subscription.

```php
if ($user->subscribed()) {
    // ...
}
```

You can utilize this in a variety of locations in your app, such as middleware, rules, and so on, to provide services. To determine whether an individual subscription is valid, you can use the `valid` method:

```php
if ($user->subscription()->valid()) {
    // ...
}
```

This method, like the subscribed method, returns true if your membership is active, on trial, past due, or cancelled during its grace period.

You may also check if a subscription is for a certain product:

```php
if ($user->subscription()->hasProduct('product_id_123')) {
    // ...
}
```

Or for a certain price:

```php
if ($user->subscription()->hasPrice('price_id_123')) {
    // ...
}
```

If you wish to check if a subscription is on a specific price while being valid, you can use:

```php
if ($user->subscribedToPrice('price_id_123')) {
    // ...
}
```

Alternatively, if you use different [subscription types](#multiple-subscriptions), you can pass a type as an additional parameter:

```php
if ($user->subscribed('swimming')) {
    // ...
}

if ($user->subscribedToPrice('price_id_123', 'swimming')) {
    // ...
}
```

#### Cancelled Status

To see if a user has cancelled their subscription, you can use the cancelled method:

```php
if ($user->subscription()->cancelled()) {
    // ...
}
```

When they are in their grace period, you can utilize the `onGracePeriod` check.

```php
if ($user->subscription()->onGracePeriod()) {
    // ...
}
```

#### Past Due Status

If a recurring payment fails, the subscription will become past due. This indicates that the subscription is still valid, but your customer's payments will be retried in two weeks.

```php
if ($user->subscription()->pastDue()) {
    // ...
}
```

#### Subscription Scopes

There are several subscription scopes available for querying subscriptions in specific states:

```php
// Get all active subscriptions...
$subscriptions = Subscription::query()->active()->get();

// Get all of the cancelled subscriptions for a specific user...
$subscriptions = $user->subscriptions()->cancelled()->get();
```

Here's all available scopes:

```php
Subscription::query()->incomplete();
Subscription::query()->incompleteExpired();
Subscription::query()->onTrial();
Subscription::query()->active();
Subscription::query()->pastDue();
Subscription::query()->unpaid();
Subscription::query()->cancelled();
```

#### Changing Plans

When a consumer is on a monthly plan, they may desire to upgrade to a better plan, alter their payments to an annual plan, or drop to a lower-cost plan. In these cases, you can allow them to swap plans by giving a different product id to the `swap` method:

```php
use App\Models\User;

$user = User::find(1);

$user->subscription()->swap('product_id_123');
```

This will change the customer's subscription plan, however billing will not occur until the next payment cycle. If you want to immediately invoice the customer, you can use the `swapAndInvoice` method instead.

```php
$user = User::find(1);

$user->subscription()->swapAndInvoice('product_id_123');
```

#### Multiple Subscriptions

In certain situations, you may wish to allow your consumer to subscribe to numerous subscription kinds. For example, a gym may provide a swimming and weight lifting subscription. You can let your customers subscribe to one or both.

To handle the various subscriptions, you can offer a type of subscription as the second argument when creating a new one:

```php
$user = User::find(1);

$checkout = $user->subscribe('product_id_123', 'swimming');
```

You can now always refer to this specific subscription type by passing the type argument when getting it:

```php
$user = User::find(1);

// Retrieve the swimming subscription type...
$subscription = $user->subscription('swimming');

// Swap plans for the gym subscription type...
$user->subscription('gym')->swap('product_id_123');

// Cancel the swimming subscription...
$user->subscription('swimming')->cancel();
```

#### Cancelling Subscriptions

To cancel a subscription, call the `cancel` method.

```php
$user = User::find(1);

$user->subscription()->cancel();
```

This will cause your subscription to be cancelled. If you cancel your subscription in the middle of the cycle, it will enter a grace period, and the ends\_at column will be updated. The customer will continue to have access to the services offered for the duration of the period. You may check the grace period by calling the `onGracePeriod` method:

```php
if ($user->subscription()->onGracePeriod()) {
    // ...
}
```

Polar does not offer immediate cancellation. To resume a subscription while it is still in its grace period, use the resume method.

```php
$user->subscription()->resume();
```

When a cancelled subscription approaches the end of its grace period, it becomes expired and cannot be resumed.

#### Subscription Trials

> \[!NOTE]
> Coming soon.

### Handling Webhooks

Polar can send webhooks to your app, allowing you to react. By default, this package handles the majority of the work for you. If you have properly configured webhooks, it will listen for incoming events and update your database accordingly. We recommend activating all event kinds so you may easily upgrade in the future.

#### Webhook Events

* `Danestves\LaravelPolar\Events\BenefitGrantCreated`
* `Danestves\LaravelPolar\Events\BenefitGrantUpdated`
* `Danestves\LaravelPolar\Events\BenefitGrantRevoked`
* `Danestves\LaravelPolar\Events\OrderCreated`
* `Danestves\LaravelPolar\Events\OrderRefunded`
* `Danestves\LaravelPolar\Events\SubscriptionActive`
* `Danestves\LaravelPolar\Events\SubscriptionCanceled`
* `Danestves\LaravelPolar\Events\SubscriptionCreated`
* `Danestves\LaravelPolar\Events\SubscriptionRevoked`
* `Danestves\LaravelPolar\Events\SubscriptionUpdated`

Each of these events has a billable `$model` object and an event `$payload`. The subscription events also include the `$subscription` object. These can be accessed via the public properties.

If you wish to respond to these events, you must establish listeners for them. For example, you may wish to react when a subscription is updated.

```php
<?php

namespace App\Listeners;

use Danestves\LaravelPolar\Events\WebhookHandled;

class PolarEventListener
{
    /**
     * Handle received Polar webhooks.
     */
    public function handle(WebhookHandled $event): void
    {
        if ($event->payload['type'] === 'subscription.updated') {
            // Handle the incoming event...
        }
    }
}
```

The [Polar documentation](https://docs.polar.sh/integrate/webhooks/events) includes an example payload.

Laravel v11 and up will automatically discover the listener. If you're using Laravel v10 or lower, you should configure it in your app's `EventServiceProvider`:

```php
<?php

namespace App\Providers;

use App\Listeners\PolarEventListener;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Danestves\LaravelPolar\Events\WebhookHandled;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        WebhookHandled::class => [
            PolarEventListener::class,
        ],
    ];
}
```

## Roadmap

* [ ] Add support for trials
  Polar itself doesn't support trials, but we can manage them by ourselves.

## Testing

```bash
composer test
```

## Changelog

Please see [CHANGELOG](https://github.com/danestves/laravel-polar/blob/main/CHANGELOG.md) for more information on what has changed recently.

## License

The MIT License (MIT). Please see [License File](https://github.com/danestves/laravel-polar/blob/main/LICENSE.md) for more information.


# NextJS
Source: https://docs.polar.sh/integrate/sdk/adapters/nextjs

Payments and Checkouts made dead simple with NextJS

```bash
pnpm install @polar-sh/nextjs zod
```

## Checkout

Create a Checkout handler which takes care of redirections.

```typescript
// checkout/route.ts
import { Checkout } from "@polar-sh/nextjs";

export const GET = Checkout({
  accessToken: process.env.POLAR_ACCESS_TOKEN,
  successUrl: process.env.SUCCESS_URL,
  server: "sandbox", // Use sandbox if you're testing Polar - omit the parameter or pass 'production' otherwise
  theme: "dark", // Enforces the theme - System-preferred theme will be set if left omitted
});
```

### Query Params

Pass query params to this route.

* products `?products=123`
* customerId (optional) `?products=123&customerId=xxx`
* customerExternalId (optional) `?products=123&customerExternalId=xxx`
* customerEmail (optional) `?products=123&customerEmail=<EMAIL>`
* customerName (optional) `?products=123&customerName=Jane`
* metadata (optional) `URL-Encoded JSON string`

## Customer Portal

Create a customer portal where your customer can view orders and subscriptions.

```typescript
// portal/route.ts
import { CustomerPortal } from "@polar-sh/nextjs";

export const GET = CustomerPortal({
  accessToken: process.env.POLAR_ACCESS_TOKEN,
  getCustomerId: (req: NextRequest) => "", // Function to resolve a Polar Customer ID
  server: "sandbox", // Use sandbox if you're testing Polar - omit the parameter or pass 'production' otherwise
});
```

## Webhooks

A simple utility which resolves incoming webhook payloads by signing the webhook secret properly.

```typescript
// api/webhook/polar/route.ts
import { Webhooks } from "@polar-sh/nextjs";

export const POST = Webhooks({
  webhookSecret: process.env.POLAR_WEBHOOK_SECRET!,
  onPayload: async (payload) => {
    // Handle the payload
    // No need to return an acknowledge response
  },
});
```

### Payload Handlers

The Webhook handler also supports granular handlers for easy integration.

* `onPayload` - Catch-all handler for any incoming Webhook event
* `onCheckoutCreated` - Triggered when a checkout is created
* `onCheckoutUpdated` - Triggered when a checkout is updated
* `onOrderCreated` - Triggered when an order is created
* `onOrderPaid` - Triggered when an order is paid
* `onOrderRefunded` - Triggered when an order is refunded
* `onRefundCreated` - Triggered when a refund is created
* `onRefundUpdated` - Triggered when a refund is updated
* `onSubscriptionCreated` - Triggered when a subscription is created
* `onSubscriptionUpdated` - Triggered when a subscription is updated
* `onSubscriptionActive` - Triggered when a subscription becomes active
* `onSubscriptionCanceled` - Triggered when a subscription is canceled
* `onSubscriptionRevoked` - Triggered when a subscription is revoked
* `onSubscriptionUncanceled` - Triggered when a subscription cancellation is reversed
* `onProductCreated` - Triggered when a product is created
* `onProductUpdated` - Triggered when a product is updated
* `onOrganizationUpdated` - Triggered when an organization is updated
* `onBenefitCreated` - Triggered when a benefit is created
* `onBenefitUpdated` - Triggered when a benefit is updated
* `onBenefitGrantCreated` - Triggered when a benefit grant is created
* `onBenefitGrantUpdated` - Triggered when a benefit grant is updated
* `onBenefitGrantRevoked` - Triggered when a benefit grant is revoked
* `onCustomerCreated` - Triggered when a customer is created
* `onCustomerUpdated` - Triggered when a customer is updated
* `onCustomerDeleted` - Triggered when a customer is deleted
* `onCustomerStateChanged` - Triggered when a customer state changes


# Nuxt
Source: https://docs.polar.sh/integrate/sdk/adapters/nuxt

Payments and Checkouts made dead simple with Nuxt

## Installation

Choose your preferred package manager to install the module:

`pnpm add @polar-sh/nuxt`

### Register the module

Add the module to your `nuxt.config.ts`:

```typescript
export default defineNuxtConfig({
  modules: ["@polar-sh/nuxt"],
});
```

## Checkout

Create a Checkout handler which takes care of redirections.

```typescript
// server/routes/api/checkout.post.ts
export default defineEventHandler((event) => {
  const {
    private: { polarAccessToken, polarCheckoutSuccessUrl, polarServer },
  } = useRuntimeConfig();

  const checkoutHandler = Checkout({
    accessToken: polarAccessToken,
    successUrl: polarCheckoutSuccessUrl,
    server: polarServer as "sandbox" | "production",
    theme: "dark", // Enforces the theme - System-preferred theme will be set if left omitted
  });

  return checkoutHandler(event);
});
```

### Query Params

Pass query params to this route.

* products `?products=123`
* customerId (optional) `?products=123&customerId=xxx`
* customerExternalId (optional) `?products=123&customerExternalId=xxx`
* customerEmail (optional) `?products=123&customerEmail=<EMAIL>`
* customerName (optional) `?products=123&customerName=Jane`
* metadata (optional) `URL-Encoded JSON string`

## Customer Portal

Create a customer portal where your customer can view orders and subscriptions.

```typescript
// server/routes/api/portal.get.ts
export default defineEventHandler((event) => {
  const {
    private: { polarAccessToken, polarCheckoutSuccessUrl, polarServer },
  } = useRuntimeConfig();

  const customerPortalHandler = CustomerPortal({
    accessToken: polarAccessToken,
    server: polarServer as "sandbox" | "production",
    getCustomerId: (event) => {
      // Use your own logic to get the customer ID - from a database, session, etc.
      return Promise.resolve("9d89909b-216d-475e-8005-053dba7cff07");
    },
  });

  return customerPortalHandler(event);
});
```

## Webhooks

A simple utility which resolves incoming webhook payloads by signing the webhook secret properly.

```typescript
// server/routes/webhook/polar.post.ts
export default defineEventHandler((event) => {
  const {
    private: { polarWebhookSecret },
  } = useRuntimeConfig();

  const webhooksHandler = Webhooks({
    webhookSecret: polarWebhookSecret,
    onPayload: async (payload) => {
      // Handle the payload
      // No need to return an acknowledge response
    },
  });

  return webhooksHandler(event);
});
```

### Payload Handlers

The Webhook handler also supports granular handlers for easy integration.

* `onPayload` - Catch-all handler for any incoming Webhook event
* `onCheckoutCreated` - Triggered when a checkout is created
* `onCheckoutUpdated` - Triggered when a checkout is updated
* `onOrderCreated` - Triggered when an order is created
* `onOrderPaid` - Triggered when an order is paid
* `onOrderRefunded` - Triggered when an order is refunded
* `onRefundCreated` - Triggered when a refund is created
* `onRefundUpdated` - Triggered when a refund is updated
* `onSubscriptionCreated` - Triggered when a subscription is created
* `onSubscriptionUpdated` - Triggered when a subscription is updated
* `onSubscriptionActive` - Triggered when a subscription becomes active
* `onSubscriptionCanceled` - Triggered when a subscription is canceled
* `onSubscriptionRevoked` - Triggered when a subscription is revoked
* `onSubscriptionUncanceled` - Triggered when a subscription cancellation is reversed
* `onProductCreated` - Triggered when a product is created
* `onProductUpdated` - Triggered when a product is updated
* `onOrganizationUpdated` - Triggered when an organization is updated
* `onBenefitCreated` - Triggered when a benefit is created
* `onBenefitUpdated` - Triggered when a benefit is updated
* `onBenefitGrantCreated` - Triggered when a benefit grant is created
* `onBenefitGrantUpdated` - Triggered when a benefit grant is updated
* `onBenefitGrantRevoked` - Triggered when a benefit grant is revoked
* `onCustomerCreated` - Triggered when a customer is created
* `onCustomerUpdated` - Triggered when a customer is updated
* `onCustomerDeleted` - Triggered when a customer is deleted
* `onCustomerStateChanged` - Triggered when a customer state changes


# Remix
Source: https://docs.polar.sh/integrate/sdk/adapters/remix

Payments and Checkouts made dead simple with Remix

```bash
pnpm install @polar-sh/remix zod
```

## Checkout

Create a Checkout handler which takes care of redirections.

```typescript
import { Checkout } from "@polar-sh/remix";

export const loader = Checkout({
  accessToken: "xxx", // Or set an environment variable to POLAR_ACCESS_TOKEN
  successUrl: process.env.SUCCESS_URL,
  server: "sandbox", // Use sandbox if you're testing Polar - omit the parameter or pass 'production' otherwise
  theme: "dark", // Enforces the theme - System-preferred theme will be set if left omitted
});
```

### Query Params

Pass query params to this route.

* products `?products=123`
* customerId (optional) `?products=123&customerId=xxx`
* customerExternalId (optional) `?products=123&customerExternalId=xxx`
* customerEmail (optional) `?products=123&customerEmail=<EMAIL>`
* customerName (optional) `?products=123&customerName=Jane`
* metadata (optional) `URL-Encoded JSON string`

## Customer Portal

Create a customer portal where your customer can view orders and subscriptions.

```typescript
import { CustomerPortal } from "@polar-sh/remix";

export const loader = CustomerPortal({
  accessToken: "xxx", // Or set an environment variable to POLAR_ACCESS_TOKEN
  getCustomerId: (event) => "", // Function to resolve a Polar Customer ID
  server: "sandbox", // Use sandbox if you're testing Polar - omit the parameter or pass 'production' otherwise
});
```

## Webhooks

A simple utility which resolves incoming webhook payloads by signing the webhook secret properly.

```typescript
import { Webhooks } from "@polar-sh/remix";

export const action = Webhooks({
  webhookSecret: process.env.POLAR_WEBHOOK_SECRET!,
  onPayload: async (payload) => /** Handle payload */,
})
```

### Payload Handlers

The Webhook handler also supports granular handlers for easy integration.

* `onPayload` - Catch-all handler for any incoming Webhook event
* `onCheckoutCreated` - Triggered when a checkout is created
* `onCheckoutUpdated` - Triggered when a checkout is updated
* `onOrderCreated` - Triggered when an order is created
* `onOrderPaid` - Triggered when an order is paid
* `onOrderRefunded` - Triggered when an order is refunded
* `onRefundCreated` - Triggered when a refund is created
* `onRefundUpdated` - Triggered when a refund is updated
* `onSubscriptionCreated` - Triggered when a subscription is created
* `onSubscriptionUpdated` - Triggered when a subscription is updated
* `onSubscriptionActive` - Triggered when a subscription becomes active
* `onSubscriptionCanceled` - Triggered when a subscription is canceled
* `onSubscriptionRevoked` - Triggered when a subscription is revoked
* `onSubscriptionUncanceled` - Triggered when a subscription cancellation is reversed
* `onProductCreated` - Triggered when a product is created
* `onProductUpdated` - Triggered when a product is updated
* `onOrganizationUpdated` - Triggered when an organization is updated
* `onBenefitCreated` - Triggered when a benefit is created
* `onBenefitUpdated` - Triggered when a benefit is updated
* `onBenefitGrantCreated` - Triggered when a benefit grant is created
* `onBenefitGrantUpdated` - Triggered when a benefit grant is updated
* `onBenefitGrantRevoked` - Triggered when a benefit grant is revoked
* `onCustomerCreated` - Triggered when a customer is created
* `onCustomerUpdated` - Triggered when a customer is updated
* `onCustomerDeleted` - Triggered when a customer is deleted
* `onCustomerStateChanged` - Triggered when a customer state changes


# Sveltekit
Source: https://docs.polar.sh/integrate/sdk/adapters/sveltekit

Payments and Checkouts made dead simple with Sveltekit

```bash
pnpm install @polar-sh/sveltekit zod
```

## Checkout

Create a Checkout handler which takes care of redirections.

```typescript
// /api/checkout/+server.ts
import { Checkout } from "@polar-sh/sveltekit";

export const GET = Checkout({
  accessToken: process.env.POLAR_ACCESS_TOKEN,
  successUrl: process.env.SUCCESS_URL,
  server: "sandbox", // Use sandbox if you're testing Polar - omit the parameter or pass 'production' otherwise
  theme: "dark", // Enforces the theme - System-preferred theme will be set if left omitted
});
```

### Query Params

Pass query params to this route.

* products `?products=123`
* customerId (optional) `?products=123&customerId=xxx`
* customerExternalId (optional) `?products=123&customerExternalId=xxx`
* customerEmail (optional) `?products=123&customerEmail=<EMAIL>`
* customerName (optional) `?products=123&customerName=Jane`
* metadata (optional) `URL-Encoded JSON string`

## Customer Portal

Create a customer portal where your customer can view orders and subscriptions.

```typescript
// /api/portal/+server.ts
import { CustomerPortal } from "@polar-sh/sveltekit";

export const GET = CustomerPortal({
  accessToken: process.env.POLAR_ACCESS_TOKEN,
  getCustomerId: (event) => "", // Function to resolve a Polar Customer ID
  server: "sandbox", // Use sandbox if you're testing Polar - omit the parameter or pass 'production' otherwise
});
```

## Webhooks

A simple utility which resolves incoming webhook payloads by signing the webhook secret properly.

```typescript
// api/webhook/polar/+server.ts
import { Webhooks } from "@polar-sh/sveltekit";

export const POST = Webhooks({
  webhookSecret: process.env.POLAR_WEBHOOK_SECRET!,
  onPayload: async (payload) => {
    // Handle the payload
  },
});
```

### Payload Handlers

The Webhook handler also supports granular handlers for easy integration.

* `onPayload` - Catch-all handler for any incoming Webhook event
* `onCheckoutCreated` - Triggered when a checkout is created
* `onCheckoutUpdated` - Triggered when a checkout is updated
* `onOrderCreated` - Triggered when an order is created
* `onOrderPaid` - Triggered when an order is paid
* `onOrderRefunded` - Triggered when an order is refunded
* `onRefundCreated` - Triggered when a refund is created
* `onRefundUpdated` - Triggered when a refund is updated
* `onSubscriptionCreated` - Triggered when a subscription is created
* `onSubscriptionUpdated` - Triggered when a subscription is updated
* `onSubscriptionActive` - Triggered when a subscription becomes active
* `onSubscriptionCanceled` - Triggered when a subscription is canceled
* `onSubscriptionRevoked` - Triggered when a subscription is revoked
* `onSubscriptionUncanceled` - Triggered when a subscription cancellation is reversed
* `onProductCreated` - Triggered when a product is created
* `onProductUpdated` - Triggered when a product is updated
* `onOrganizationUpdated` - Triggered when an organization is updated
* `onBenefitCreated` - Triggered when a benefit is created
* `onBenefitUpdated` - Triggered when a benefit is updated
* `onBenefitGrantCreated` - Triggered when a benefit grant is created
* `onBenefitGrantUpdated` - Triggered when a benefit grant is updated
* `onBenefitGrantRevoked` - Triggered when a benefit grant is revoked
* `onCustomerCreated` - Triggered when a customer is created
* `onCustomerUpdated` - Triggered when a customer is updated
* `onCustomerDeleted` - Triggered when a customer is deleted
* `onCustomerStateChanged` - Triggered when a customer state changes


# TanStack Start
Source: https://docs.polar.sh/integrate/sdk/adapters/tanstack-start

Payments and Checkouts made dead simple with TanStack Start

<CodeGroup>
  ```bash npm
  npm install @polar-sh/tanstack-start zod
  ```

  ```bash pnpm
  pnpm add @polar-sh/tanstack-start zod
  ```

  ```bash yarn
  yarn add @polar-sh/tanstack-start zod
  ```

  ```bash bun
  bun install @polar-sh/tanstack-start zod
  ```
</CodeGroup>

## Checkout

Create a Checkout handler which takes care of redirections.

```typescript
// routes/api/checkout.ts
import { Checkout } from "@polar-sh/tanstack-start";
import { createAPIFileRoute } from "@tanstack/react-start/api";

export const APIRoute = createAPIFileRoute("/api/checkout")({
  GET: Checkout({
    accessToken: process.env.POLAR_ACCESS_TOKEN,
    successUrl: process.env.SUCCESS_URL,
    server: "sandbox", // Use sandbox if you're testing Polar - omit the parameter or pass 'production' otherwise
    theme: "dark", // Enforces the theme - System-preferred theme will be set if left omitted
  }),
});
```

### Query Params

Pass query params to this route.

* products `?products=123`
* customerId (optional) `?products=123&customerId=xxx`
* customerExternalId (optional) `?products=123&customerExternalId=xxx`
* customerEmail (optional) `?products=123&customerEmail=<EMAIL>`
* customerName (optional) `?products=123&customerName=Jane`
* metadata (optional) `URL-Encoded JSON string`

## Customer Portal

Create a customer portal where your customer can view orders and subscriptions.

```typescript
// routes/api/portal.ts
import { CustomerPortal } from "@polar-sh/tanstack-start";
import { createAPIFileRoute } from "@tanstack/react-start/api";
import { getSupabaseServerClient } from "~/servers/supabase-server";

export const APIRoute = createAPIFileRoute("/api/portal")({
  GET: CustomerPortal({
    accessToken: process.env.POLAR_ACCESS_TOKEN,
    getCustomerId: async (request: Request) => "", // Function to resolve a Polar Customer ID
    server: "sandbox", // Use sandbox if you're testing Polar - omit the parameter or pass 'production' otherwise
  }),
});
```

## Webhooks

A simple utility which resolves incoming webhook payloads by signing the webhook secret properly.

```typescript
// api/webhook/polar.ts
import { Webhooks } from "@polar-sh/tanstack-start";
import { createAPIFileRoute } from "@tanstack/react-start/api";

export const APIRoute = createAPIFileRoute("/api/webhook/polar")({
  POST: Webhooks({
    webhookSecret: process.env.POLAR_WEBHOOK_SECRET!,
    onPayload: async (payload) => {
      // Handle the payload
      // No need to return an acknowledge response
    },
  }),
});
```

#### Payload Handlers

The Webhook handler also supports granular handlers for easy integration.

* `onPayload` - Catch-all handler for any incoming Webhook event
* `onCheckoutCreated` - Triggered when a checkout is created
* `onCheckoutUpdated` - Triggered when a checkout is updated
* `onOrderCreated` - Triggered when an order is created
* `onOrderPaid` - Triggered when an order is paid
* `onOrderRefunded` - Triggered when an order is refunded
* `onRefundCreated` - Triggered when a refund is created
* `onRefundUpdated` - Triggered when a refund is updated
* `onSubscriptionCreated` - Triggered when a subscription is created
* `onSubscriptionUpdated` - Triggered when a subscription is updated
* `onSubscriptionActive` - Triggered when a subscription becomes active
* `onSubscriptionCanceled` - Triggered when a subscription is canceled
* `onSubscriptionRevoked` - Triggered when a subscription is revoked
* `onSubscriptionUncanceled` - Triggered when a subscription cancellation is reversed
* `onProductCreated` - Triggered when a product is created
* `onProductUpdated` - Triggered when a product is updated
* `onOrganizationUpdated` - Triggered when an organization is updated
* `onBenefitCreated` - Triggered when a benefit is created
* `onBenefitUpdated` - Triggered when a benefit is updated
* `onBenefitGrantCreated` - Triggered when a benefit grant is created
* `onBenefitGrantUpdated` - Triggered when a benefit grant is updated
* `onBenefitGrantRevoked` - Triggered when a benefit grant is revoked
* `onCustomerCreated` - Triggered when a customer is created
* `onCustomerUpdated` - Triggered when a customer is updated
* `onCustomerDeleted` - Triggered when a customer is deleted
* `onCustomerStateChanged` - Triggered when a customer state changes


# Go SDK
Source: https://docs.polar.sh/integrate/sdk/golang



Documentation coming soon.


# PHP SDK
Source: https://docs.polar.sh/integrate/sdk/php



Documentation coming soon.


# Python SDK
Source: https://docs.polar.sh/integrate/sdk/python



Fully type-hinted and allows both synchronous and asynchronous usage, thanks to [HTTPX](https://www.python-httpx.org/).
Under the hood, schemas are validated by [Pydantic](https://docs.pydantic.dev/latest/).

### Quickstart

```bash
pip install polar-sdk
```

```python
# Synchronous Example
from polar_sdk import Polar

s = Polar(
    access_token="<YOUR_BEARER_TOKEN_HERE>",
)


res = s.users.benefits.list()

if res is not None:
    while True:
        # handle items

        res = res.Next()
        if res is None:
            break
```

[Read more](https://github.com/polarsource/polar-python)

### Sandbox Environment

You can configure the SDK so it hits the [sandbox environment](/integrate/sandbox) instead of the production one. You just need to add the `server` argument when instantiating the client:

```python
s = Polar(
    server="sandbox",
    access_token="<YOUR_BEARER_TOKEN_HERE>",
)
```


# TypeScript SDK
Source: https://docs.polar.sh/integrate/sdk/typescript

SDK for JavaScript runtimes (Node.js and browsers)

### Quickstart

```bash
pnpm add @polar-sh/sdk
```

```typescript
import { Polar } from '@polar-sh/sdk'

const polar = new Polar({
  accessToken: process.env['POLAR_ACCESS_TOKEN'] ?? '',
})

async function run() {
  const result = await polar.users.benefits.list({})

  for await (const page of result) {
    // Handle the page
    console.log(page)
  }
}

run()
```

[Read more](https://github.com/polarsource/polar-js)

<Note>
  **camelCase vs. snake\_case**

  Our API (and docs) is designed with `snake_case`. However, our TS SDK currently
  converts this to camelCase to follow JS/TS convention. You should automatically
  see the camelCase parameters suggested in modern IDEs due to typing, but it's
  worth keeping in mind switching between code & docs.

  We aim to introduce the ability to toggle this in the future, i.e using
  `snake_case` even in TypeScript to more easily map it to our documentation and
  design of the API itself.
</Note>

### Framework Adapters

Implement Checkout & Webhook handlers in 5 lines of code.

* [Next.js](/integrate/sdk/adapters/nextjs)
* [Astro](/integrate/sdk/adapters/astro)
* [Remix](/integrate/sdk/adapters/remix)
* [Sveltekit](/integrate/sdk/adapters/sveltekit)
* [Deno](/integrate/sdk/adapters/deno)
* [Elysia](/integrate/sdk/adapters/elysia)
* [Express](/integrate/sdk/adapters/express)
* [Fastify](/integrate/sdk/adapters/fastify)
* [Hono](/integrate/sdk/adapters/hono)

### Sandbox Environment

You can configure the SDK so it hits the [sandbox environment](/integrate/sandbox) instead of the production one. You just need to add the `server` property to the configuration object:

```typescript
const polar = new Polar({
  server: 'sandbox',
  accessToken: process.env['POLAR_ACCESS_TOKEN'] ?? '',
})
```


# Handle & monitor webhook deliveries
Source: https://docs.polar.sh/integrate/webhooks/delivery

How to parse, validate and handle webhooks and monitor their deliveries on Polar

<img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/integrate/webhooks/delivery.light.png" />

<img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/integrate/webhooks/delivery.dark.png" />

Once a webhook endpoint is setup you will have access to the delivery overview
page. Here you can:

* See historic deliveries
* Review payload sent
* Trigger redelivery in case of failure

Now, let's integrate our endpoint route to validate, parse & handle incoming webhooks.

## Validate & parse webhooks

You now need to setup a route handler for the endpoint registered on Polar to
receive, validate and parse webhooks before handling them according to your
needs.

### Using our SDKs

Our TypeScript & Python SDKs come with a built-in helper function to easily
validate and parse the webhook event - see full examples below.

<CodeGroup>
  ```typescript JS (Express)
  import express, { Request, Response } from 'express'
  import { validateEvent, WebhookVerificationError } from '@polar-sh/sdk/webhooks'

  const app = express()

  app.post(
  '/webhook',
  express.raw({ type: 'application/json' }),
  (req: Request, res: Response) => {
  try {
  const event = validateEvent(
  req.body,
  req.headers,
  process.env['POLAR_WEBHOOK_SECRET'] ?? '',
  )

        // Process the event

        res.status(202).send('')
      } catch (error) {
        if (error instanceof WebhookVerificationError) {
          res.status(403).send('')
        }
        throw error
      }

  },
  )

  ```

  ```python Python (Flask)
  import os
  from flask import Flask, request
  from polar_sdk.webhooks import validate_event, WebhookVerificationError

  app = Flask(__name__)

  @app.route('/webhook', methods=['POST'])
  def webhook():
      try:
          event = validate_event(
              body=request.data,
              headers=request.headers,
              secret=os.getenv('POLAR_WEBHOOK_SECRET', ''),
          )

          # Process the event

          return "", 202
      except WebhookVerificationError as e:
          return "", 403
  ```
</CodeGroup>

Both examples above expect an environment variable named `POLAR_WEBHOOK_SECRET`
to be set to the secret you configured during the endpoint setup.

### Custom validation

We follow the [Standard Webhooks](https://www.standardwebhooks.com/)
standard which offers [many libraries across languages](https://github.com/standard-webhooks/standard-webhooks/tree/main/libraries) to
easily validate signatures. Or you can follow their
[specification](https://github.com/standard-webhooks/standard-webhooks/blob/main/spec/standard-webhooks.md)
in case you want to roll your own.

<Info>
  **Note: Secret needs to be base64 encoded**

  One common gotcha with the specification is that the webhook secret is expected to be
  base64 encoded. You don't have to do this with our SDK as it takes care of the
  implementation details with better developer ergonomics.
</Info>

## IP Allowlist

If you are using a firewall or a reverse proxy that requires IP allowlisting, here are the IPs you need to allow:

<CodeGroup>
  ```txt Production
  ************
  *************
  *************
  ```

  ```txt Sandbox
  ************
  *************
  *************
  ```
</CodeGroup>

## Failure Handling

### Delivery Retries

If we hit an error while trying to reach your endpoint, whether it is a temporary network error or a bug, we'll retry to send the event up to **10 times** with an exponential backoff.

### Delivery Timeouts

We timeout our requests to your endpoint after **20 seconds**. Triggering a
retry attempt after a delay as explained above. However, we strongly recommend you optimize your endpoint route to be fast. A
best practice is for your webhook handler to queue a background worker task to handle the
payload asynchronously.

## Troubleshooting

### Not receiving webhooks

Seeing deliveries on Polar, but not receiving them on your end? Below are some
common techniques to resolve the issue depending on the reported error status.

**General**

*Start ngrok or similar*

Make sure you have started `ngrok` or whatever tunneling service you're using
during local development.

*Add excessive logging*

E.g
`console.log('webhook.handler_called')`,
`console.log('webhook.validate_signature')`,
`console.log('webhook.signature_validated')` etc.

So you can easily confirm if the handler is called and how far it gets before
any issues arise.

`HTTP 404`

* Try `curl -vvv -X POST <copy-paste-endpoint-url>` in your terminal to confirm the
  route exists and see any issues along the way
* Try adding trailing `/` to the URL on Polar. Often `/foo` is resolved to
  `/foo/` by frameworks.

`HTTP 403`

* Using middleware for authorization? Make sure to exclude the webhook route
  from it since it needs to be publicly accessible
* Using Cloudflare? Check the firewall logs to verify if they are blocking our
  requests and setup a custom WAF rule to accept incoming requests from Polar.

### Invalid signature exceptions

Rolling your own webhook validation logic? Make sure to base64 encode the secret
you configured on Polar in your code before generating the signature to validate
against.


# Setup Webhooks
Source: https://docs.polar.sh/integrate/webhooks/endpoints

Get notifications asynchronously when events occur instead of having to poll for updates

Our webhook implementation follows the [Standard Webhooks](https://www.standardwebhooks.com/) specification
and our SDKs offer:

* Built-in webhook signature validation for security
* Fully typed webhook payloads

In addition, our webhooks offer built-in support for **Slack** & **Discord**
formatting. Making it a breeze to setup in-chat notifications for your team.

## Get Started

<Info>
  **Use our sandbox environment during development**

  So you can easily test purchases, subscriptions, cancellations and refunds to
  automatically trigger webhook events without spending a dime.
</Info>

<Steps>
  <Step title="Add new endpoint">
    Head over to your organization settings and click on the `Add Endpoint` button to create a new webhook.

    <img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/integrate/webhooks/create.light.png" />

    <img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/integrate/webhooks/create.dark.png" />
  </Step>

  <Step title="Specify your endpoint URL">
    Enter the URL to which the webhook events should be sent.

    <img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/integrate/webhooks/url.light.png" />

    <img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/integrate/webhooks/url.dark.png" />

    <Tip>
      **Developing locally?**

      Use a tool like [ngrok](https://ngrok.com/) to tunnel webhook events to your local development environment. This will allow you to test your webhook handlers without deploying them to a live server.

      Once you have `ngrok` you can easily start a tunnel:

      ```bash
      ngrok http 3000
      ```

      Just be sure to provide the URL ngrok gives you as the webhook endpoint on
      Polar.
    </Tip>
  </Step>

  <Step title="Choose a delivery format">
    For standard, custom integrations, leave this parameter on **Raw**. This will send a payload in JSON format.

    If you wish to send notifications to a Discord or Slack channel, you can select the corresponding format here. Polar will then adapt the payload so properly formatted messages are sent to your channel.

    <img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/integrate/webhooks/format.light.png" />

    <img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/integrate/webhooks/format.dark.png" />

    If you paste a Discord or Slack Webhook URL, the format will be automatically selected.
  </Step>

  <Step title="Set a secret">
    We cryptographically sign the requests using this secret. So you can easily
    verify them using our SDKs to ensure they are legitimate webhook payloads
    from Polar.

    You can set your own or generate a random one.

    <img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/integrate/webhooks/secret.light.png" />

    <img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/integrate/webhooks/secret.dark.png" />
  </Step>

  <Step title="Subscribe to events">
    Finally, select all the events you want to be notified about and you're done 🎉
  </Step>
</Steps>

[Now, it's time to integrate our endpoint to receive events
→](/integrate/webhooks/delivery)


# Webhook Events
Source: https://docs.polar.sh/integrate/webhooks/events

Our webhook events and in which context they are useful

## Billing Events

### Checkout

<Card title="checkout.created" icon="link" href="/api-reference/webhooks/checkout.created" horizontal />

<Card title="checkout.updated" icon="link" href="/api-reference/webhooks/checkout.updated" horizontal />

### Customers

<Card title="customer.created" icon="link" href="/api-reference/webhooks/customer.created" horizontal>
  Fired when a new customer has been created.
</Card>

<Card title="customer.updated" icon="link" href="/api-reference/webhooks/customer.updated" horizontal>
  Fired when a customer has been updated.
</Card>

<Card title="customer.deleted" icon="link" href="/api-reference/webhooks/customer.deleted" horizontal>
  Fired when a customer has been deleted.
</Card>

<Card title="customer.state_changed" icon="link" href="/api-reference/webhooks/customer.state_changed" horizontal>
  Fired when a customer's state has changed. Includes active subscriptions and
  granted benefits.
</Card>

### Subscriptions

In order to properly implement logic for handling subscriptions, you should look into the following events.

<Card title="subscription.created" icon="link" href="/api-reference/webhooks/subscription.created" horizontal>
  Fired when a new subscription has been created.
</Card>

<Card title="subscription.updated" icon="link" href="/api-reference/webhooks/subscription.updated" horizontal>
  Use this event if you want to handle cancellations, un-cancellations, etc. The
  updated event is a catch-all event for `subscription.active` ,
  `subscription.canceled`, `subscription.uncanceled` and `subscription.revoked`.
</Card>

<Card title="order.created" icon="link" href="/api-reference/webhooks/order.created" horizontal>
  In case you want to do logic when a subscription is renewed, you should listen
  to `order.created` and the `billing_reason` field. It can be `purchase`,
  `subscription_create`, `subscription_cycle` and `subscription_update`.
  `subscription_cycle` is used when subscriptions renew.
</Card>

<Card title="subscription.active" icon="link" href="/api-reference/webhooks/subscription.active" horizontal />

<Card title="subscription.canceled" icon="link" href="/api-reference/webhooks/subscription.canceled" horizontal />

<Card title="subscription.uncanceled" icon="link" href="/api-reference/webhooks/subscription.uncanceled" horizontal />

<Card title="subscription.revoked" icon="link" href="/api-reference/webhooks/subscription.revoked" horizontal />

### Order

<Card title="order.created" icon="link" href="/api-reference/webhooks/order.created" horizontal />

<Card title="order.paid" icon="link" href="/api-reference/webhooks/order.paid" horizontal />

<Card title="order.updated" icon="link" href="/api-reference/webhooks/order.updated" horizontal />

<Card title="order.refunded" icon="link" href="/api-reference/webhooks/order.refunded" horizontal />

### Refunds

<Card title="refund.created" icon="link" href="/api-reference/webhooks/refund.created" horizontal />

<Card title="refund.updated" icon="link" href="/api-reference/webhooks/refund.updated" horizontal />

### Benefit Grants

<Card title="benefit_grant.created" icon="link" href="/api-reference/webhooks/benefit_grant.created" horizontal />

<Card title="benefit_grant.updated" icon="link" href="/api-reference/webhooks/benefit_grant.updated" horizontal />

<Card title="benefit_grant.revoked" icon="link" href="/api-reference/webhooks/benefit_grant.revoked" horizontal />

## Organization Events

### Benefits

<Card title="benefit.created" icon="link" href="/api-reference/webhooks/benefit.created" horizontal />

<Card title="benefit.updated" icon="link" href="/api-reference/webhooks/benefit.updated" horizontal />

### Products

<Card title="product.created" icon="link" href="/api-reference/webhooks/product.created" horizontal />

<Card title="product.updated" icon="link" href="/api-reference/webhooks/product.updated" horizontal />

### Organization

<Card title="organization.updated" icon="link" href="/api-reference/webhooks/organization.updated" horizontal />


# Polar: Modern Billing Infrastructure for Developers
Source: https://docs.polar.sh/introduction

Open-source Merchant of Record platform with developer-first APIs, automated tax compliance, and 20% lower fees than traditional solutions

<img height="200" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/welcome.png" />

<Note>
  **TL;DR**: Polar is an open-source billing platform that handles global tax
  compliance, automates product delivery, and integrates with 5 lines of code.
  Start selling in minutes, not months.
</Note>

## What is Polar?

Polar is an **open-source billing infrastructure platform** designed specifically for developers, designers, and digital creators who want to monetize their products without the complexity of traditional payment systems.

<CardGroup cols={2}>
  <Card title="Beyond Payment Processing" icon="credit-card">
    Unlike Stripe that only handles transactions, we provide complete billing
    infrastructure with tax compliance, product management, and automated access
    & delivery.
  </Card>

  <Card title="Merchant of Record" icon="globe">
    We handle all international tax compliance, so you can sell globally without
    worrying about VAT, GST, or sales tax regulations.
  </Card>
</CardGroup>

## Problems We Solve

<AccordionGroup>
  <Accordion title="Tax Compliance Nightmare" icon="building-columns">
    **The Problem**: Selling digital products globally means dealing with VAT, GST, and sales tax in dozens of jurisdictions, each with different rates, rules, and filing requirements. Most developers either ignore this (risky) or avoid international sales entirely.

    **Polar's Solution**: As your Merchant of Record, we handle all international tax compliance. We calculate, collect, and remit taxes worldwide. You focus on building; we handle the paperwork.
  </Accordion>

  <Accordion title="Complex Billing Infrastructure" icon="gears">
    **The Problem**: Building subscription billing, product catalogs, customer portals, and payment flows from scratch takes months of development time and ongoing maintenance.

    **Polar's Solution**: Complete billing infrastructure out-of-the-box with APIs that let you integrate in minutes. No need to build customer portals, handle subscription lifecycle, or manage failed payments.
  </Accordion>

  <Accordion title="Manual Access & Delivery Overhead" icon="box">
    **The Problem**: Manually sending license keys, granting repository access, or managing Discord invites for every purchase doesn't scale and creates delays for customers.

    **Polar's Solution**: Automated benefit delivery for common developer needs - license keys, file downloads, GitHub repo access, Discord roles, and more. Customers get instant access.
  </Accordion>

  <Accordion title="High Processing Costs" icon="money-bill">
    **The Problem**: Traditional MoR solutions charge 5-8% per transaction plus monthly fees, eating into your profits before you even start.

    **Polar's Solution**: 20% lower fees at just 4% + 40¢ per transaction with no monthly minimums. We earn when you earn.
  </Accordion>
</AccordionGroup>

## Core Features

### Flexible Product Management

<CardGroup cols={3}>
  <Card title="One-time Purchases" icon="cart-shopping">
    Sell digital products, courses, templates, or software licenses with instant
    delivery
  </Card>

  <Card title="Subscriptions" icon="arrows-rotate">
    Recurring billing with automatic renewals and dunning management
  </Card>

  <Card title="Flexible Pricing" icon="hand-holding-dollar">
    Fixed price, pay-what-you-want, or free products with optional minimums
  </Card>
</CardGroup>

### Powerful Checkout Experience

<img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/introduction/checkout.light.png" />

<img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/introduction/checkout.dark.png" />

<CardGroup cols={3}>
  <Card title="Checkout Links" icon="link" href="/features/checkout/links">
    No-code solution for quick product sales. Create and share instantly.
  </Card>

  <Card title="Embedded Checkout" icon="browser" href="/features/checkout/embed">
    Integrate seamlessly into your website with customizable branding.
  </Card>

  <Card title="Checkout API" icon="code" href="/api-reference">
    Programmatically create dynamic checkout sessions for custom flows.
  </Card>
</CardGroup>

### Automated Benefits (Entitlements)

**Set it and forget it**: Configure once, and customers get instant access to
their benefits automatically. No manual work required.

<CardGroup cols={2}>
  <Card title="License Keys" icon="key" href="/features/benefits/license-keys">
    Generate and deliver software licenses automatically with custom formats
  </Card>

  <Card title="File Downloads" icon="download" href="/features/benefits/file-downloads">
    Secure delivery of digital assets up to 10GB with download tracking
  </Card>

  <Card title="GitHub Access" icon="github" href="/features/benefits/github-access">
    Auto-invite customers to private repositories and manage permissions
  </Card>

  <Card title="Discord Access" icon="discord" href="/features/benefits/discord-access">
    Automatic role assignment and server invites for community access
  </Card>
</CardGroup>

### Global Merchant of Record

* **Worldwide tax compliance** - We handle VAT, GST, and sales tax in all jurisdictions
* **EU VAT handling** - Proper B2B reverse charge and B2C tax collection
* **Automatic tax calculation** - Real-time tax rates for every transaction

<Warning>
  **Important**: We handle tax compliance in all major markets including US, EU,
  UK, and more. We continuously expand coverage based on customer needs.
</Warning>

## Quick Start Guide

<Steps>
  <Step title="Create Your Account">
    [Sign up for Polar](https://polar.sh/signup) using GitHub, Google, or email. Create an organization to manage your products and customers.

    <img className="block dark:hidden" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/create-org.light.png" />

    <img className="hidden dark:block" src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/create-org.dark.png" />
  </Step>

  <Step title="Create Your First Product">
    Set up a digital product in minutes:

    * Choose between one-time purchase or subscription
    * Set your pricing (fixed, pay-what-you-want, or free)
    * Configure automated benefits for instant delivery

    Learn more about [Products →](/features/products)
  </Step>

  <Step title="Choose Your Integration">
    Pick the approach that fits your needs:

    <Tabs>
      <Tab title="No-Code (Fastest)">
        Perfect for getting started quickly:

        * Create [Checkout Links](/features/checkout/links) from your dashboard
        * Share via email, social media, or embed in websites
        * Start accepting payments immediately
      </Tab>

      <Tab title="Embedded">
        Integrate into your existing website:

        * Add our [Embedded Checkout](/features/checkout/embed) component
        * Maintain your site's look and feel
        * Customers never leave your domain
      </Tab>

      <Tab title="Full API (Maximum Control)">
        Maximum flexibility for custom workflows:

        * Use our [SDKs](/integrate/sdk/typescript) for any language
        * Build custom checkout flows and experiences
        * Integrate with your existing tech stack
      </Tab>
    </Tabs>
  </Step>

  <Step title="Set Up Webhooks">
    Stay synchronized with customer events:

    * Configure webhook endpoints in your dashboard
    * React to purchases, subscription changes, and customer events
    * Keep your database in sync automatically

    Read the [Webhooks guide →](/integrate/webhooks/endpoints)
  </Step>
</Steps>

## Integration Options

### Framework Adapters (Recommended)

<CardGroup cols={2}>
  <Card title="Next.js" icon="react" href="/integrate/sdk/adapters/nextjs">
    React-based full-stack framework with App Router support
  </Card>

  <Card title="SvelteKit" icon="js" href="/integrate/sdk/adapters/sveltekit">
    Svelte-based full-stack framework with TypeScript support
  </Card>

  <Card title="Laravel" icon="php" href="/integrate/sdk/adapters/laravel">
    PHP web application framework with Eloquent ORM integration
  </Card>

  <Card title="Express" icon="node-js" href="/integrate/sdk/adapters/express">
    Minimal and flexible Node.js web application framework
  </Card>
</CardGroup>

<Expandable title="All 12 supported frameworks">
  <CardGroup cols={4}>
    <Card title="Nuxt" href="/integrate/sdk/adapters/nuxt">
      Vue.js framework
    </Card>

    <Card title="Remix" href="/integrate/sdk/adapters/remix">
      React framework
    </Card>

    <Card title="Fastify" href="/integrate/sdk/adapters/fastify">
      Fast Node.js
    </Card>

    <Card title="Hono" href="/integrate/sdk/adapters/hono">
      Cloudflare Workers
    </Card>

    <Card title="Deno" href="/integrate/sdk/adapters/deno">
      Modern runtime
    </Card>

    <Card title="Tanstack Start" href="/integrate/sdk/adapters/tanstack-start">
      Full-stack React
    </Card>

    <Card title="Elysia" href="/integrate/sdk/adapters/elysia">
      Bun framework
    </Card>

    <Card title="Astro" href="/integrate/sdk/adapters/astro">
      Static site generator
    </Card>
  </CardGroup>
</Expandable>

### Native SDKs

<CardGroup cols={4}>
  <Card title="JS/TS" icon="js" href="/integrate/sdk/typescript">
    For web and Node.js applications
  </Card>

  <Card title="Python" icon="python" href="/integrate/sdk/python">
    For Django, Flask, FastAPI frameworks
  </Card>

  <Card title="Go" icon="golang" href="/integrate/sdk/golang">
    For Go web services and applications
  </Card>

  <Card title="PHP" icon="php" href="/integrate/sdk/php">
    For WordPress, Laravel, and PHP apps
  </Card>
</CardGroup>

## Why Choose Polar?

<Tabs>
  <Tab title="Individual Developers">
    <CardGroup cols={2}>
      <Card title="Ship Faster" icon="rocket">
        Focus on your product, not billing infrastructure. Get to market weeks faster.
      </Card>

      <Card title="Global Reach" icon="globe">
        Sell worldwide without worrying about tax compliance or regional restrictions.
      </Card>

      <Card title="Automated Delivery" icon="box">
        License keys and downloads handled automatically. No manual work required.
      </Card>

      <Card title="Lower Costs" icon="piggy-bank">
        20% cheaper than competitors with transparent, pay-as-you-earn pricing.
      </Card>
    </CardGroup>
  </Tab>

  <Tab title="Small Teams">
    <CardGroup cols={2}>
      <Card title="No Engineering Overhead" icon="wrench">
        Complete billing solution without months of custom development work.
      </Card>

      <Card title="Scalable Pricing" icon="chart-line">
        Pay only when you earn, no monthly minimums or setup fees.
      </Card>

      <Card title="Team Collaboration" icon="users">
        Multiple team members can manage products, customers, and analytics.
      </Card>

      <Card title="Professional Checkout" icon="credit-card">
        Branded experience that builds customer trust and increases conversions.
      </Card>
    </CardGroup>
  </Tab>

  <Tab title="Growing Businesses">
    <CardGroup cols={2}>
      <Card title="Enterprise Features" icon="building">
        Advanced analytics, custom fields, bulk operations, and priority support.
      </Card>

      <Card title="API Flexibility" icon="code">
        Full programmatic control over products, customers, orders, and subscriptions.
      </Card>

      <Card title="Webhook Reliability" icon="link">
        Reliable real-time synchronization with your systems and database.
      </Card>
    </CardGroup>
  </Tab>
</Tabs>

## Transparent Pricing

<CardGroup cols={2}>
  <Card title="4% + 40¢" icon="percent">
    **Per successful transaction**

    Simple, transparent pricing with no surprises
  </Card>

  <Card title="$0" icon="dollar-sign">
    **Monthly fees or setup costs**

    Pay only when you earn, no fixed costs
  </Card>
</CardGroup>

<Info>
  **Additional fees may apply**: Some transactions may incur additional fees
  (international cards, subscriptions). Payout fees are charged by payment
  providers. See our [detailed fees page](/merchant-of-record/fees) for complete
  information.
</Info>

## Open Source & Community

Polar is built in the open with full transparency and a growing community of
contributors.

<CardGroup cols={2}>
  <Card title="Open Source Codebase" icon="github" href="https://github.com/polarsource/polar">
    Apache 2.0 license with 36+ contributors and growing
  </Card>

  <Card title="Public Development" icon="users" href="https://github.com/polarsource/polar/issues">
    Feature requests, roadmap, and issues - all developed in public
  </Card>

  <Card title="Transparent Pricing" icon="eye">
    No hidden fees or surprise charges. What you see is what you pay.
  </Card>

  <Card title="Community Support" icon="discord" href="https://discord.gg/Pnhfz3UThd">
    Join our Discord for help, feedback, and feature discussions
  </Card>
</CardGroup>

<Warning>
  While self-hosting is technically possible, we recommend using our hosted
  service to get the full Merchant of Record benefits including global tax
  compliance.
</Warning>

## Ready to Start?

<CardGroup cols={2}>
  <Card title="Create Account" icon="user-plus" href="https://polar.sh/signup">
    **Free signup, no credit card required**

    Get started in under 2 minutes
  </Card>

  <Card title="Read the Guides" icon="book" href="/guides/nextjs">
    **Framework-specific tutorials**

    Step-by-step integration guides
  </Card>

  <Card title="Explore the API" icon="code" href="/api-reference">
    **Complete API documentation**

    Interactive examples and SDKs
  </Card>

  <Card title="Join Our Community" icon="discord" href="https://discord.gg/Pnhfz3UThd">
    **Get help from our team**

    Active community and support
  </Card>
</CardGroup>


# null
Source: https://docs.polar.sh/merchant-of-record/acceptable-use



As your Merchant of Record (MoR), we are the reseller of all digital goods and
services and focus exclusively on digital products. Therefore we cannot support
physical goods or entirely human services, e.g consultation or support. In
addition to not accepting the sale of anything illegal, harmful, abusive,
deceptive or sketchy.

## Acceptable Products & Businesses

* Software & SaaS
* Digital products: Templates, eBooks, PDFs, code, icons, fonts, design assets, photos, videos, audio etc
* Premium content & access: Discord server, GitHub repositories, courses and content requiring a subscription.

**General rule of acceptable services**

Digital goods, software or services that can be fulfilled by…

1. Polar on your behalf (License Keys, File Downloads, GitHub- or Discord invites or private links, e.g premium YouTube videos etc)
2. Your site/service using our APIs to grant immediate access to digital assets
   or services for customers with a one-time purchase or subscriptions

Combined with being something you’d proudly boast about in public, i.e nothing illegal, unfair, deceptive, abusive, harmful or shady.

Don’t hesitate to [reach out to us](/support) in advance in case you’re unsure if your use case would be approved.

## Prohibited Businesses

<Note>
  **Not an exhaustive list**

  We reserve the right to add to it at any time. Combined with placing your
  account under further review or suspend it in case we consider the usage
  deceptive, fraudulent, high-risk or of low quality for consumers with high
  refund/chargeback risks.
</Note>

* Illegal or age restricted, e.g drugs, alcohol, tobacco or vaping products
* Violates laws in the jurisdictions where your business is located or to which your business is targeted
* Violates any rules or regulations from payment processors & credit card networks, e.g [Stripe](https://stripe.com/en-se/legal/restricted-businesses)
* Reselling or distributing customer data to other parties for commercial, promotional or any other reason (disclosed service providers are accepted).
* Threatens reputation of Polar or any of our partners and payment providers
* Causes or has a significant risk of refunds, chargebacks, fines, damages, or harm and liability
* Services used by-, intended for or advertised towards minors
* Physical goods of any kind. Including SaaS services offering or requiring fulfilment via physical delivery or human services.
* Human services, e.g marketing, design, web development and consulting in general.
* Donations or charity, i.e price is greater than product value or there is no exchange at all (pure money transfer). Open source maintainers with sponsorship can be supported - reach out.
* Marketplaces. Selling others’ products or services using Polar against an upfront payment or with an agreed upon revenue share.
* Adult services or content. Including by AI or proxy, e.g
  * AI Girlfriend/Boyfriend services.
  * OnlyFans related services.
  * Explicit/NSFW content generated with AI
* Low-quality products, services or sites, e.g
  * E-books generated with AI or 4 pages sold for \$50
  * Quickly & poorly executed websites, products or services
  * Services with a lot of bugs and issues
  * Products, services or websites we determine to have a low trust score
* Fake testimonials, reviews, and social proof. It's deceptive to consumers which is behaviour we do not tolerate.
* Trademark violations
* "Get rich" schemes or content
* Gambling & betting services
* Regulated services or products
* Counterfeit goods
* Job boards
* NFT & Crypto assets.
* Cheating: Utilizing cheat codes, hacks, or any unauthorized modifications that alter gameplay or provide an unfair advantage.
* Reselling Licenses: Selling, distributing, or otherwise transferring software licenses at reduced prices or without proper authorization.
* Services to circumvent rules or terms of other services: Attempting to bypass, manipulate, or undermine any established rules, gameplay mechanics, or pricing structures of other vendors/games.
* Financial services, e.g facilitating transactions, investments or balances for customers.
* Financial advice, e.g content or services related to tax guidance, wealth management, investment strategies etc.
* IPTV services
* Virus & Spyware
* Telecommunication & eSIM Services
* Products you don’t own the IP of or have the required licenses to resell
* Advertising & unsolicited marketing services. Including services to:
  * Generate, scrape or sell leads
  * Send SMS/WhatsApp messages in bulk
  * Automate outreach (spam risks)
  * Automate mass content generation & submission across sites
* API & IP cloaking services, e.g services to circumvent IP bans, API rate limits etc.
* Products or services associated with pseudo-science; clairvoyance, horoscopes, fortune-telling etc.
* Travel services, reservation services, travel clubs and timeshares
* Medical advice services or products, e.g. pharmaceutical, weight loss, muscle building.

## Restricted Businesses

Requires closer review and a higher bar of quality, execution, trust and compliance
standards to be accepted.

* Directories & boards
* Marketing services
* Pre-orders & Paid waitlist
* Ticket sales

## FAQ

**Why do directories & boards require closer review?**

They often sell premium placement, i.e ads, without meeting compliance
requirements for advertising. Or even where it's their sole purpose to sell
placement.

**Why do marketing services require closer review?**

Too many services offer sketchy marketing tactics and mass outreach
(unsolicited) features. There is no short-cut to sales beyond offering a great
product & service. We love marketing services that reflect that and focus on the
long game vs. shortcuts and hacks.

**Can I sell pre-orders or use paid waitlists for my service to validate demand
before build?**

Generally, no. It's a high risk category for us as the Merchant of Record.
Sellers could withdraw funds and never deliver the service or not as promised.
Causing consumers to demand refunds or dispute the sale against us at a later
date.

For high-trust cases from developers with a track record, we're able to make
exceptions, but simultaneously need to adapt our payout process to withhold all
funds until verified fulfilment.

**Why are marketplaces or human services (consultancy) not allowed?**

We hope to change this status quo amongst Merchants of Record long-term, but
both come with additional compliance and risk challenges. Since fulfilment is
not digital, immediate or between known parties to us, we cannot fulfil our
compliance & risk requirements or effectively mitigate potential disputes.

**Why are OnlyFans services not allowed?**

Close & blurred lines between the service and the content & service provided on
OnlyFans, i.e often adult content. In addition to us having seen
fraudulent & deceptive behavior in the category. We're simply not comfortable
acting as the Merchant of Record here.


# null
Source: https://docs.polar.sh/merchant-of-record/account-reviews



As a Merchant of Record (MoR), we act as the reseller of digital goods and services. Therefore, we need to make sure that businesses using Polar complies with our [acceptable products & use](/merchant-of-record/acceptable-use) policies. Combined with continuously monitoring, reviewing and preventing fraud, unacceptable use, bad actors and high risk accounts.

### First payout review (24-48h)

You will need to go through our main review ahead of the initial payout. We’ll reach out over email within 24h (often faster) with:

1. A quick survey about your business, products and intended use case with Polar
2. Identity verification (KYC) using passport (or drivers license) and selfie. It’s secure, easy and quick to submit using Stripe Identity.

We need to perform this review to ensure compliance with our [acceptable products & use](/merchant-of-record/acceptable-use) policy. Combined with meeting our own KYC/AML requirements as a billing platform.

**Submit upfront (Soon)**

We’ll soon offer the ability to submit all of this information in advance to speed up the initial payout even further and without concern of any issues or delays.

### Continuous reviews (Async)

We continuously monitor all transactions across our platform to proactively prevent fraud. In addition to performing asynchronous reviews of accounts at certain sale thresholds. These reviews are often completed within hours and without any additional information required from you.

You’ll get notified over email that a review is taking place. Payouts will be paused during this time, but it has no impact on your customers’ experience or ability to purchase, subscribe or checkout at any time.

We look at:

* Risk scores across historic transactions
* Refund- & Chargeback ratio
* Appropriate next sales threshold for a review given the above

**High chargeback ratios**

Credit card networks, e.g Visa/Mastercard, consider 0.7% of sales in chargebacks excessive. Exceeding it can lead to monitoring programs with high costs, penalties and ultimately termination.

We therefore reach out proactively to collaborate on maintaining a low chargeback ratio and reducing it ahead of getting close to these thresholds.

## Operational Guidelines

To maintain platform integrity and ensure smooth operations, we have established clear guidelines for merchants using Polar.

#### Expected Responsiveness

We expect merchants to maintain high standards of customer support and responsiveness:

* **Support Ticket Management**: Maintain a low rate of support tickets from end-users
* **Merchant Communication**: When we include you in customer support communications, we require a response within 48 hours
  * If no response is received within 48 hours, we will issue a warning
  * If no response is received within 24 hours of the warning, we may issue refunds to affected customers
* **Customer Service Quality**: We evaluate your customer service history and approach when determining appropriate actions

#### Test Transactions

To maintain platform security and prevent abuse:

* **Use Sandbox Environment or 100% Discounts**: All  testing must be conducted using our sandbox environment. Want to test in production? Use a Free Product or a 100% Discount code to avoid using real money.
* **No Real Money Testing**: Any such transactions are prohibited and will be refunded. It's against the terms of payment service providers, triggers our account reviews and can potentially lead to the card or account getting blocked since it can be flagged as "card testing".

#### Chargeback Management

We monitor chargeback rates closely to protect both merchants and customers:

* **Acceptable Threshold**: We maintain a chargeback rate threshold of 0.4% for merchants
* **Timeframe**: Chargebacks can be filed up to 120 days from the original transaction date
* **Consequences of High Chargeback Rates**:

  We work proactively to maintain a low chargeback rate throughout the platform and strive to collaborate with merchants at risk of exceeding our thresholds to reverse the trend. However, we reserve the right to (in order of severity):

  * Refund transactions as needed
  * Pause payouts pending review – up until the timeframe for chargebacks have been surpassed
  * Pause future payments
  * Block accounts and refund customers

  We don't take this responsibility or actions lightly, and always strive to mitigate and avoid them, but have to take appropriate and proactive actions in case of issues.

  <Note>
    We also have integrations with credit card networks to receive early chargeback signals before they're officially filed. We automatically refund such transactions under a certain value and cancel any subscriptions associated with the customer to reduce chargebacks proactively.
  </Note>

#### Policy Violations

For merchants who violate our [acceptable use policies](/merchant-of-record/acceptable-use) (and don't have high chargeback rates):

* **Immediate Action**: We will offboard merchants who violate our policies
* **Payment Processing**: All payment processing will be blocked
* **Payout Management**: Payouts will be paused pending review
* **Resolution Process**:
  * We may conduct test transactions to verify account status
  * In case of strong suspicion of fraud or intentional abuse, we block the account immediately
  * We reach out merchants about the issue and give them 48 hours to respond
    * Failure to respond may result in refunds to affected customers
  * We pause future payments in the meantime if deemed necessary
  * We pause payouts during the resolution process
  * We strive to collaborate with merchants on the best possible path forward. Clear fraud or abuse, however, is immediately blocked.
  * We have to cancel subscriptions and refund payments made in violation of our acceptable use policies for compliance and risk, and strive to do so in collaboration with the merchant.


# Fees
Source: https://docs.polar.sh/merchant-of-record/fees

Transparent fees at a 20% discount vs. other MoRs

## Transaction Fees

All transactions on Polar come with a small fee of 4% + 40¢ - applied to the entire transaction amount.

Polar is currently built on Stripe, and we cover their 2.9% + 30¢ fee from ours. However, they impose a few additional fees for certain transactions that we need to pass on.

### **Additional Fees**

* ****% for international cards (non-US)
* +0.5% for subscription payments
* *We also reserve the right to pass on any other fees Stripe might impose in the future*

**Example**

Let's look at an example breakdown with all these additional fees applied. Below is a payment of a \$30 subscription from Sweden (25% VAT).

| Item                           | Amount     |
| ------------------------------ | ---------- |
| Product Price                  | \$30       |
| VAT (25%)                      | \$7.5      |
| **Total Transaction Value**    | **\$37.5** |
| Transaction Fee (4% + 40¢)     | \$1.9      |
| International Card (****%)     | \$0.56     |
| Subscription (+0.5%)           | \$0.19     |
| **Total Fees (Before Payout)** | **\$2.65** |

### Refunds

You can issue both full or partial refunds on Polar to your customers. However, the initial transaction fees are not refunded to you since credit card networks and PSPs charge them regardless of a future refund.

Please note: Polar reserves the right to issue refunds at our own discretion up to 60 days after the purchase as part of our efforts to continuously and proactively reduce disputes & chargebacks which costs you \$15/dispute. We only leverage this right for this purpose and in the interest of reducing chargebacks and fees for you.

### Dispute/Chargeback Fees

Sometimes, customers can open a **dispute/chargeback** via their bank for a purchase. **Disputes cost \$15 per dispute** regardless of outcome and is deducted from your balance directly. This fee is charged by the underlying credit card networks & PSPs regardless of outcome and therefore something we cannot refund.

However, we continuously work to proactively reduce the rate of chargebacks across Polar to be at or lower than industry standards.

Credit card networks impose monitoring programs, penalties and higher chargeback costs for sellers with high chargeback rates (\~0.7%+). Since Polar is the Merchant of Record, we therefore always monitor and proactively prevent our rate coming close to these thresholds.

Therefore, we might need to intervene and even suspend your account unless swift and proactive measures are taken to reduce chargebacks to an acceptable industry standard.

## Payout Fees

While payouts may incur fees charged by the payout providers (such as Stripe), Polar does not add any extra fees or markup. These are strictly the provider’s fees, and Polar does not profit from them.

In addition, Polar offers manual withdrawals for developers. Keeping you in control of when to issue payouts.

*Unless you have a Polar balance that you haven't withdrawn for several months, at which point we'll eventually need to trigger a payout on your behalf.*

**Stripe**

* \$2 per month of active payout(s)
* 0.25% + \$0.25 per payout
* Cross border fees (currency conversion): 0.25% (EU) - 1% in other countries.

**Open Collective (Deprecated for new users)**

* 10% on amount transferred

## Volume pricing

Large or fast-growing business? We can offer custom pricing to better fit your needs. [Reach out to us](/support).


# Merchant of Record
Source: https://docs.polar.sh/merchant-of-record/introduction

An open source and transparent Merchant of Record

### What is a Merchant of Record?

We take on the liability of international sales taxes globally for you.
So you can focus on growing your business vs. accounting bills. Leave billing
infrastructure and international sales tax headaches to us.

### Payment Service Providers vs. Merchants of Record

**Payment Service Providers (PSPs)**

<img src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/merchant-of-record/introduction/psp.jpeg" />

Stripe and other Payment Service Providers (PSPs) offer an accessible and convenient abstraction to faciliate transactions on top of underlying credit card networks & banks.

* ✅ Powerful, flexibile & low-level APIs to facilitate transactions
* ✅ Can be used to power all business- and pricing models under the sun.
* ❌ You are responsible for all liabilities associated with transactions, e.g international taxes
* ❌ Low-level APIs require more development even for common use cases

**Merchants of Record (MoRs)**

<img src="https://mintlify.s3.us-west-1.amazonaws.com/polar/assets/merchant-of-record/introduction/mor.jpeg" />

Merchants of Record offer yet another layer of convenient abstraction to facilitate digital orders on top of the underlying PSPs and transactions. E.g Polar is built on Stripe (+ more PSPs in the future).

* ✅ Higher-level Dashboard, APIs & SDKs to better facilitate digital products, services & orders beyond the underlying transactions
* ✅ The platform (Polar) handles international taxes by being a reseller of your digital goods & services. Of course, without being in the way of your relationship with your customers.
* ❌ Less flexibility & control in terms of advanced business- and pricing models.
* ❌ Higher fees per payment

**What should you choose?**

**Ship with what you feel comfortable with vs. others tell you to**

Just like in programming, abstractions are super helpful to ship faster with fewer low-level concerns, but in exchange for reduced flexibility and higher costs. So what's the right level of abstraction for you? As always, it depends (tm).

**Go with Stripe (PSP) if...**

* You've already integrated it? Just ship already - we salute builders however they ship
* You're comfortable with the Stripe API and prefer absolute control with low-level APIs.
* You're looking for the lowest fees possible.
* You're fine with handling international taxes yourself (you absolutely can).

**Go with Polar (MoR) if...**

* You want product-, customer-, order- and subscription management via an intuitive and easy dashboard
* You want to offer file downloads, license keys, Discord- and/or private GitHub repository invites with ease - with more built-in automations to come.
* You prefer a more high-level API optimized for making monetization easier. We're only getting started here and have some big things coming
* You want us to handle international taxes for you

### Polar MoR

**tl;dr We take on the liability of international sales taxes globally for you. So you can focus on building your passion. Leaving billing infrastructure and sales tax headaches to us.**

So how does Polar offer a Merchant of Record (MoR) service and handle international sale taxes? All other Merchants of Record simply state they handle it internationally - don't worry about it. We do too.

But we believe in transparency and don't want to scare customers into thinking it's impossible to manage it themselves. So below we'll share how exactly we go about doing this.

#### International Sales Taxes

Most countries, states and jurisdictions globally impose sales taxes on digital goods and services (VAT, GST, US Sales Tax etc). Regardless of whether the merchant (seller) is a resident there or not - they're doing business there.

For example, a \$10/month subscription should cost \$12.5/month for a Swedish (25% VAT) consumer, but \$10/month for a Swedish business with VAT registration (reverse charge).

Merchants are responsible for 1) capturing & 2) remitting sales taxes to the local tax authorities. What does that mean in our example?

1. **Capturing**. Charging the Swedish consumer \$12.5/month and saving \$2.5/month for the Swedish tax authorities. Stripe Tax is an excellent service to automate this and the one Polar uses today.
2. **Remitting**. Filing & paying the captured sales taxes with the tax authorities on time. Stripe Tax does not do this, i.e the merchant is liable to register, file and pay taxes to local tax authorities.

Many jurisdictions, however, don't require this until you reach a certain threshold in terms of sales volume. But others require registration even before the first sale - or after a very low threshold. In addition to having different rates and rules on which goods are taxable and whether they're deductable or not for business customers.

For example, United Kingdom and EU countries require upfront registration for international companies, but Texas (United States) does not until you've sold for more than \$500,000 🇺🇸🦅

In short: It's complex and hard. Even large and well-known businesses don't do it perfectly. Arguably, it's almost impossible and at least highly impracticle and expensive to comply perfectly upfront. Many companies even delay compliance as a calculated risk, i.e focus on validating & growing their business with the risk of paying back taxes + penalities later.

**PSP (Stripe)**

* ✅ Your volume alone is what counts towards international thresholds vs. the MoR platform, i.e customers might not need to pay sales taxes with you, but would via a MoR.
* ✅ You can deduct inbound VAT against purchases your business does with VAT
* ❌ You're liable for capturing & remitting international sales taxes
* ❌ Stripe Tax is great to monitor & automate capturing, but registration and remittance is up to you.

**MoR (Polar)**

* ✅ We are liable for all of the above as your reseller, i.e we have to worry about it vs. you.
* ✅ Offer EU VAT for B2B sales (expected and desired within EU for businesses) without having to register, capture and remit it yourself.
* ❌ Sales taxes would be added for more customers vs. with you selling directly
* ❌ You cannot leverage inbound VAT towards VAT expense deductions yourself

Merchants of Record (MoR) handles sales taxes, e.g US Sales Tax, EU VAT,
Canadian GST etc. **However, you're always responsible for your own
income/revenue tax** in your country of residency.

#### Polar Coverage

**tl;dr We support global payments and are liable for all international sales taxes. We continuously monitor and work with our accounting firms to expand registrations as needed on our end.**

**Global Payments & Tax Liabilities**

As your Merchant of Record, Polar is liable for tax compliance globally on all sales internationally via our platform, hosted- or embedded checkoutsfrom payments anywhere in the world.

**Current Polar Tax Registrations**

1. Polar Software Inc. is incorporated as a US Delaware C Corp and will register for US State Sales Taxes upon reaching thresholds
2. EU VAT (Irish OSS VAT)
3. UK VAT

No Merchant of Record (MoR) or business registers upfront in all global jurisdictions. Since it would be 1) unnecessary in case of thresholds & 2) incredibly expensive with uncertain return on investment (ROI) in all markets.

We work with global accounting firms specialized in registering, filing and remitting taxes in all countries. So we can easily scale registrations and remittance as needed. Below is our process and evaluation for expanding registrations.

**Expanding Registrations**

Below are the fees the global acounting firms we work with charge us - on average per market:

* \~\$500 upfront for registration
* \~\$300 per filing and remittance (\~quarterly)
* *Excluding consultations (billed hourly) and our internal efforts and automations to transform Stripe Tax reports into correct output for the accounting firms.*

So on average $1,700 in year one and $1,200 therafter for each market at a minimum. Businesses (and you if you handle this yourself) therefore need to ask themselves: Do I anticipate more in sales from a given market vs. costs of operating there?

Let's imagine a country with 20% sales tax.

1. At $6,000+ the tax liability start outgrowing the accounting costs for you standalone ($1,200/20%)
2. Polar with a 1.1% premium vs. Stripe would need to help facilitate $109,090 in sales for the given market in order for it to cover our accounting costs ($1,200/1.1%)

Our customers are selling mostly in the US, UK & EU. Given US thresholds and our current registrations, it's therefore a non-issue.

In markets we're not registered, we still have the liability and take it on (#1) to assess the potential for our customers and us long-term. In addition to being comfortable betting on markets a lot earlier than it becomes profitable for us (#2).

However, in case of neither we reserve the right to block payments from such countries in the short-term until the opportunity for our customers and us changes in the given market.

**Want to do this yourself?**

Selling a lot and want to handle this yourself, i.e worth the ongoing costs? Feel free to reach out and we'd be happy to introduce you to our contacts at the accounting firms we use.

We consider MoR a key value-add to Polar, but not the sole reason for Polar to exist. Our ambition is to be the easiest way to monetize for developers. However, we're never going to be the right solution for all use cases. But we'll always salute and help anyone who ships software - regardless of billing platform.


# null
Source: https://docs.polar.sh/merchant-of-record/supported-countries



### Payments & Merchant of Record

We support payments globally except from countries with US sanctions.

As your Merchant of Record (MoR) we take on the liability for international sales taxes - [read more here](/merchant-of-record/introduction).

### Payouts

Polar uses Stripe Connect Express to issue payouts to residents or businesses in any of the countries below.

<Note>
  **FAQ: Stripe is not supported in my country**

  Stripe Connect Express for payouts is a separate product from Stripe Payments.

  In some cases, Stripe Payments might not be available for merchants in your country, but Stripe Connect Express is for payouts using cross-border transfers.

  Since Polar is the Merchant of Record and uses Stripe Connect Express for payouts, we're able to support sellers in all of the countries below.
</Note>

* 🇦🇱 Albania
* 🇩🇿 Algeria
* 🇦🇴 Angola
* 🇦🇬 Antigua and Barbuda
* 🇦🇷 Argentina
* 🇦🇲 Armenia
* 🇦🇺 Australia
* 🇦🇹 Austria
* 🇦🇿 Azerbaijan
* 🇧🇸 Bahamas
* 🇧🇭 Bahrain
* 🇧🇩 Bangladesh
* 🇧🇪 Belgium
* 🇧🇯 Benin
* 🇧🇹 Bhutan
* 🇧🇴 Bolivia
* 🇧🇦 Bosnia and Herzegovina
* 🇧🇼 Botswana
* 🇧🇳 Brunei
* 🇧🇬 Bulgaria
* 🇰🇭 Cambodia
* 🇨🇦 Canada
* 🇨🇱 Chile
* 🇨🇴 Colombia
* 🇨🇷 Costa Rica
* 🇭🇷 Croatia
* 🇨🇾 Cyprus
* 🇨🇿 Czech Republic
* 🇩🇰 Denmark
* 🇩🇴 Dominican Republic
* 🇪🇨 Ecuador
* 🇪🇬 Egypt
* 🇸🇻 El Salvador
* 🇪🇪 Estonia
* 🇪🇹 Ethiopia
* 🇫🇮 Finland
* 🇫🇷 France
* 🇬🇦 Gabon
* 🇬🇲 Gambia
* 🇩🇪 Germany
* 🇬🇭 Ghana
* 🇬🇷 Greece
* 🇬🇹 Guatemala
* 🇬🇾 Guyana
* 🇭🇰 Hong Kong
* 🇭🇺 Hungary
* 🇮🇸 Iceland
* 🇮🇳 India
* 🇮🇩 Indonesia
* 🇮🇪 Ireland
* 🇮🇱 Israel
* 🇮🇹 Italy
* 🇨🇮 Ivory Coast
* 🇯🇲 Jamaica
* 🇯🇵 Japan
* 🇯🇴 Jordan
* 🇰🇿 Kazakhstan
* 🇰🇪 Kenya
* 🇰🇼 Kuwait
* 🇱🇦 Laos
* 🇱🇻 Latvia
* 🇱🇮 Liechtenstein
* 🇱🇹 Lithuania
* 🇱🇺 Luxembourg
* 🇲🇴 Macao
* 🇲🇬 Madagascar
* 🇲🇾 Malaysia
* 🇲🇹 Malta
* 🇲🇺 Mauritius
* 🇲🇽 Mexico
* 🇲🇩 Moldova
* 🇲🇨 Monaco
* 🇲🇳 Mongolia
* 🇲🇦 Morocco
* 🇲🇿 Mozambique
* 🇳🇦 Namibia
* 🇳🇱 Netherlands
* 🇳🇿 New Zealand
* 🇳🇪 Niger
* 🇳🇬 Nigeria
* 🇲🇰 North Macedonia
* 🇳🇴 Norway
* 🇴🇲 Oman
* 🇵🇰 Pakistan
* 🇵🇦 Panama
* 🇵🇾 Paraguay
* 🇵🇪 Peru
* 🇵🇭 Philippines
* 🇵🇱 Poland
* 🇵🇹 Portugal
* 🇶🇦 Qatar
* 🇷🇴 Romania
* 🇷🇼 Rwanda
* 🇱🇨 Saint Lucia
* 🇸🇲 San Marino
* 🇸🇦 Saudi Arabia
* 🇸🇳 Senegal
* 🇷🇸 Serbia
* 🇸🇬 Singapore
* 🇸🇰 Slovakia
* 🇸🇮 Slovenia
* 🇿🇦 South Africa
* 🇰🇷 South Korea
* 🇪🇸 Spain
* 🇱🇰 Sri Lanka
* 🇸🇪 Sweden
* 🇨🇭 Switzerland
* 🇹🇼 Taiwan
* 🇹🇿 Tanzania
* 🇹🇭 Thailand
* 🇹🇹 Trinidad and Tobago
* 🇹🇳 Tunisia
* 🇹🇷 Turkey
* 🇦🇪 United Arab Emirates
* 🇬🇧 United Kingdom
* 🇺🇸 United States
* 🇺🇾 Uruguay
* 🇺🇿 Uzbekistan
* 🇻🇳 Vietnam

## Frequently Asked Questions

<AccordionGroup>
  <Accordion title="Can I use Polar in countries (e.g. India) where Stripe is invite-only?">
    <Note>Stripe Connect Express is a different product than the regular Stripe payments.</Note>
    Yes, any individual or company operating in our [supported countries](/merchant-of-record/supported-countries) can receive payouts from Polar even if Stripe standalone is invite-only there.

    This is possible as Polar is the Merchant of Record, all payments from customers are made to Polar (US). [Stripe Connect Express](https://docs.stripe.com/connect/express-accounts) is then used to issue payouts, and is supported in more countries via cross-border transfer than Stripe Payments standalone.

    You might still see a warning in Stripe Connect Express that payments are invite-only, but don't worry. No direct sales are made directly to the Stripe Connect Express account. They're all made to Polar (US) as a platform and the merchant of record. We only use the transfer and payout feature of Stripe Connect Express which is available in all of our [supported countries](/merchant-of-record/supported-countries).
  </Accordion>

  <Accordion title="Can I use Polar as an individual to make sales globally?">
    Yes, given that Stripe Connect Express supports individual as a business type in your region.

    To know which business type is supported in your country, follow steps as below:

    * Open required [verification information](https://docs.stripe.com/connect/required-verification-information#US+RS+express+recipient+individual+transfers) by Stripe to set up a business or personal account in your country.
    * Ensure `Platform Country` is set to `United States (US)`.
    * Ensure `Dashboard Type` is set to `express`.
    * Ensure `Service Agreement` is set to `recipient`.
    * Ensure `Capability` is set to `transfers`.
    * Select the correct `Account Country` relevant to you.
    * Click on the toggle for `Business Type` which will allow you know if individual, business, company or LLC/LLP is supported by Stripe Connect Express in that region.
  </Accordion>
</AccordionGroup>


# Migrate to Polar
Source: https://docs.polar.sh/migrate

Get set up on Polar in minutes from an existing store

## Lemon Squeezy

Ready to make the jump from Lemon Squeezy to Polar? Use the `polar-migrate` CLI tool to quickly and easily migrate your existing Lemon Squeezy products to Polar.

### Getting Started

```bash
npx polar-migrate
```

### Supported Migrations

* Products & Variants
* License Keys
* Associated Files
* Discount Codes
* Customers

This tool is not able to move **active** subscriptions from your Lemon Squeezy store.

### Open Source

The code for the CLI is open source and available on GitHub

[View Code on GitHub](https://github.com/polarsource/polar-migrate)

## Paddle, Stripe, Gumroad or others?

[Reach out to us](mailto:<EMAIL>) and we'd be happy to help.


# Support
Source: https://docs.polar.sh/support

We love to support customers, quickly. Reach out anytime.

Thank you for reaching out and helping us make Polar & our docs better. We greatly appreciate it!

## GitHub

* [Ask a question](https://github.com/orgs/polarsource/discussions/categories/q-a)
* [Found a bug?](https://github.com/polarsource/polar/issues)
* [Have a feature request?](https://github.com/orgs/polarsource/discussions/categories/feature-requests)

## Discord

[Join our Discord](https://dub.sh/polar-discord) to chat with us and fellow Polar developers.

## Email

You can reach us at [<EMAIL>](mailto:<EMAIL>)

