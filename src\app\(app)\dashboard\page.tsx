import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import { MessageSquare, Users, TrendingUp, Settings } from "lucide-react";

export default async function DashboardPage() {
  const supabase = await createClient();

  const { data, error } = await supabase.auth.getClaims();
  if (error || !data?.claims) {
    redirect("/auth/login");
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold">Dashboard</h1>
        <p className="text-muted-foreground">
          Welcome back! Here&apos;s an overview of your customer communications.
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-card p-6 rounded-lg border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Total Conversations
              </p>
              <p className="text-2xl font-bold">1,234</p>
            </div>
            <MessageSquare className="h-8 w-8 text-muted-foreground" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Active Customers
              </p>
              <p className="text-2xl font-bold">567</p>
            </div>
            <Users className="h-8 w-8 text-muted-foreground" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Response Rate
              </p>
              <p className="text-2xl font-bold">89%</p>
            </div>
            <TrendingUp className="h-8 w-8 text-muted-foreground" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Avg Response Time
              </p>
              <p className="text-2xl font-bold">2.4h</p>
            </div>
            <Settings className="h-8 w-8 text-muted-foreground" />
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-card p-6 rounded-lg border">
        <h2 className="text-xl font-semibold mb-4">Recent Activity</h2>
        <div className="space-y-4">
          <div className="flex items-center justify-between py-2">
            <div>
              <p className="font-medium">New conversation started</p>
              <p className="text-sm text-muted-foreground">
                Customer: <EMAIL>
              </p>
            </div>
            <span className="text-sm text-muted-foreground">2 min ago</span>
          </div>
          <div className="flex items-center justify-between py-2">
            <div>
              <p className="font-medium">Message replied</p>
              <p className="text-sm text-muted-foreground">
                Customer: <EMAIL>
              </p>
            </div>
            <span className="text-sm text-muted-foreground">5 min ago</span>
          </div>
          <div className="flex items-center justify-between py-2">
            <div>
              <p className="font-medium">Conversation resolved</p>
              <p className="text-sm text-muted-foreground">
                Customer: <EMAIL>
              </p>
            </div>
            <span className="text-sm text-muted-foreground">10 min ago</span>
          </div>
        </div>
      </div>
    </div>
  );
}
