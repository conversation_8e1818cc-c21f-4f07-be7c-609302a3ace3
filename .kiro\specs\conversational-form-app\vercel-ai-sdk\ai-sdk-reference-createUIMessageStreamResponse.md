
# `createUIMessageStreamResponse`

The `createUIMessageStreamResponse` function creates a Response object that streams UI messages to the client.

## Import

<Snippet
  text={`import { createUIMessageStreamResponse } from "ai"`}
  prompt={false}
/>

## Example

```tsx
import { createUIMessageStream, createUIMessageStreamResponse } from 'ai';

const response = createUIMessageStreamResponse({
  status: 200,
  statusText: 'OK',
  headers: {
    'Custom-Header': 'value',
  },
  stream: createUIMessageStream({
    execute({ writer }) {
      // Write custom data
      writer.write({
        type: 'data',
        value: { message: 'Hello' },
      });

      // Write text content
      writer.write({
        type: 'text',
        value: 'Hello, world!',
      });

      // Write source information
      writer.write({
        type: 'source-url',
        value: {
          type: 'source',
          id: 'source-1',
          url: 'https://example.com',
          title: 'Example Source',
        },
      });

      // Merge with LLM stream
      const result = streamText({
        model: openai('gpt-4'),
        prompt: 'Say hello',
      });

      writer.merge(result.toUIMessageStream());
    },
  }),
});
```

## API Signature

### Parameters

<PropertiesTable
  content={[
    {
      name: 'stream',
      type: 'ReadableStream<UIMessageChunk>',
      description: 'The UI message stream to send to the client.',
    },
    {
      name: 'status',
      type: 'number',
      isOptional: true,
      description: 'The status code for the response. Defaults to 200.',
    },
    {
      name: 'statusText',
      type: 'string',
      isOptional: true,
      description: 'The status text for the response.',
    },
    {
      name: 'headers',
      type: 'Headers | Record<string, string>',
      isOptional: true,
      description: 'Additional headers for the response.',
    },
    {
      name: 'consumeSseStream',
      type: '(options: { stream: ReadableStream<string> }) => PromiseLike<void> | void',
      isOptional: true,
      description:
        'Optional callback to consume the Server-Sent Events stream.',
    },
  ]}
/>

### Returns

`Response`

A Response object that streams UI message chunks with the specified status, headers, and content.
